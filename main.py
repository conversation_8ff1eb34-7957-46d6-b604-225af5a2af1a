# main.py
from fastapi import <PERSON>AP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request

# 导入API路由
from app.api.v1.endpoints import headers_converter, json_formatter, xml_formatter
from app.api.v1.endpoints import html_formatter
from app.api.v1.endpoints import url_parser
from app.api.v1.endpoints import base64_converter
from app.api.v1.endpoints import datetime_converter
from app.api.v1.endpoints import text_converter

app = FastAPI(title="爬虫工具箱")

app.mount("/static", StaticFiles(directory="app/static"), name="static")
templates = Jinja2Templates(directory="app/templates")

# 引入API路由
app.include_router(headers_converter.router, prefix="/api/v1", tags=["Headers"])
app.include_router(json_formatter.router, prefix="/api/v1", tags=["JSON"])
app.include_router(xml_formatter.router, prefix="/api/v1", tags=["XML"])
app.include_router(html_formatter.router, prefix="/api/v1", tags=["HTML"])
app.include_router(url_parser.router, prefix="/api/v1", tags=["URL"])
app.include_router(base64_converter.router, prefix="/api/v1", tags=["Base64"])
app.include_router(datetime_converter.router, prefix="/api/v1", tags=["DateTime"])
app.include_router(text_converter.router, prefix="/api/v1", tags=["TextConverter"])

@app.get("/")
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})