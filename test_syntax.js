// 简单的语法测试文件
console.log('Testing JavaScript syntax...');

// 测试async/await语法
async function testAsync() {
    try {
        const response = await fetch('/test');
        console.log('Async test passed');
    } catch (error) {
        console.log('Async test failed:', error);
    }
}

// 测试事件监听器
document.addEventListener('DOMContentLoaded', () => {
    const button = document.getElementById('test-btn');
    if (button) {
        button.addEventListener('click', async () => {
            await testAsync();
        });
    }
});

console.log('Syntax test completed');
