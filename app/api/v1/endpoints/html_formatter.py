# 文件路径: app/api/v1/endpoints/html_formatter.py

from fastapi import APIRouter
from pydantic import BaseModel
from app.core.logic import html_formatter

router = APIRouter()

class HtmlFormatRequest(BaseModel):
    raw_html: str

class HtmlFormatResponse(BaseModel):
    html_output: str
    plain_output: str
    error: str | None = None

@router.post("/format-html", response_model=HtmlFormatResponse)
async def format_html_endpoint(request: HtmlFormatRequest):
    print(f"\n--- 6. [PY Endpoint] /format-html 接口收到请求 (前100字符): {request.raw_html[:100]}... ---")
    html_output, plain_output, error = html_formatter.format_html_code(request.raw_html)
    print(f"--- 7. [PY Endpoint] 准备返回给前端的 html_output (前200字符): {html_output[:200]}... ---")
    return HtmlFormatResponse(html_output=html_output, plain_output=plain_output, error=error)