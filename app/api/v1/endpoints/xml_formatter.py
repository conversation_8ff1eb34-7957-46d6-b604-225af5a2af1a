# -*- coding: utf-8 -*-
from fastapi import APIRouter
from pydantic import BaseModel
from typing import Optional  # 也可以使用 | 联合类型

# 导入核心逻辑模块
from app.core.logic import xml_formatter

# 创建一个API路由
router = APIRouter()


# 定义请求和响应模型
class XmlFormatRequest(BaseModel):
    raw_xml: str
    compress: bool = False


class XmlFormatResponse(BaseModel):
    xml_html: str
    xml_plain: str
    # 【已修正】将error字段的类型明确为 str 或 None
    error: str | None = None


@router.post("/format-xml", response_model=XmlFormatResponse)
async def format_xml_endpoint(request: XmlFormatRequest):
    """
    接收原始XML字符串并返回格式化后的版本。
    """
    html_output, plain_output, error = xml_formatter.format_xml_to_code(
        request.raw_xml,
        compress=request.compress
    )

    return XmlFormatResponse(
        xml_html=html_output,
        xml_plain=plain_output,
        error=error
    )
