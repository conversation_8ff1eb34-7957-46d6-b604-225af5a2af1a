# 文件路径: app/api/v1/endpoints/url_parser.py

from fastapi import APIRouter
from pydantic import BaseModel
from app.core.logic import url_parser

router = APIRouter()

class UrlParseRequest(BaseModel):
    raw_url: str

class UrlParseResponse(BaseModel):
    base_url_html: str
    base_url_plain: str
    params_html: str
    params_plain: str
    error: str | None = None

@router.post("/parse-url", response_model=UrlParseResponse)
async def parse_url_endpoint(request: UrlParseRequest):
    base_url_html, base_url_plain, params_html, params_plain, error = url_parser.parse_url_and_extract_params(
        request.raw_url
    )
    return UrlParseResponse(
        base_url_html=base_url_html,
        base_url_plain=base_url_plain,
        params_html=params_html,
        params_plain=params_plain,
        error=error
    )