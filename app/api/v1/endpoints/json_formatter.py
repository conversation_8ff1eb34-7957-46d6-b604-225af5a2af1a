# -*- coding: utf-8 -*-
from fastapi import APIRouter, Body
from pydantic import BaseModel

# 导入核心逻辑模块
from app.core.logic import json_formatter

# 创建一个API路由
router = APIRouter()

# 定义请求和响应模型
class JsonFormatRequest(BaseModel):
    raw_json: str
    sort_keys: bool = False
    compress: bool = False

class JsonFormatResponse(BaseModel):
    json_html: str
    json_plain: str
    error: str = None

@router.post("/format-json", response_model=JsonFormatResponse)
async def format_json_endpoint(request: JsonFormatRequest):
    """
    接收原始JSON或Python字典字符串并返回格式化后的版本。
    """
    if not request.raw_json.strip():
        return JsonFormatResponse(json_html="", json_plain="", error="输入内容为空。")
    try:
        html_output, plain_output = json_formatter.format_json_to_code(
            request.raw_json,
            sort_keys=request.sort_keys,
            compress=request.compress
        )
        return JsonFormatResponse(json_html=html_output, json_plain=plain_output)
    except Exception as e:
        return JsonFormatResponse(json_html="", json_plain="", error=str(e))
