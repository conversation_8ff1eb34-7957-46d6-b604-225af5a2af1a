# -*- coding: utf-8 -*-
from fastapi import APIRouter, Body
from pydantic import BaseModel

# 导入核心逻辑模块
from app.core.logic import header_parser

# 创建一个API路由
router = APIRouter()


# 使用Pydantic定义请求体模型
# 这能确保我们收到的数据是我们期望的结构
class HeadersRequest(BaseModel):
    raw_headers: str


# 定义响应体模型
class HeadersResponse(BaseModel):
    headers_html: str
    headers_plain: str
    cookies_html: str
    cookies_plain: str
    error: str = None


@router.post("/convert-headers", response_model=HeadersResponse)
async def convert_headers(request: HeadersRequest):
    """
    接收原始的headers文本，并返回格式化后的Python代码（用于显示和复制）。
    """
    try:
        # 从请求中获取原始文本
        raw_headers_text = request.raw_headers
        if not raw_headers_text.strip():
            return HeadersResponse(
                headers_html="", headers_plain="",
                cookies_html="", cookies_plain="",
                error="输入内容为空。"
            )

        # 使用核心逻辑来解析headers
        headers_dict = header_parser.parse_raw_headers_to_dict(raw_headers_text)
        headers_html_str, headers_plain_str = header_parser.format_dict_to_python_code(headers_dict, "headers")

        # 从headers中解析cookies
        cookies_dict = {}
        # 统一使用小写键来查找'cookie'，更健壮
        lower_keys_headers = {k.lower(): v for k, v in headers_dict.items()}
        if 'cookie' in lower_keys_headers:
            cookies_dict = header_parser.parse_cookies_from_string(lower_keys_headers['cookie'])

        cookies_html_str, cookies_plain_str = header_parser.format_dict_to_python_code(cookies_dict, "cookies")

        return HeadersResponse(
            headers_html=headers_html_str,
            headers_plain=headers_plain_str,
            cookies_html=cookies_html_str,
            cookies_plain=cookies_plain_str
        )

    except Exception as e:
        return HeadersResponse(
            headers_html="", headers_plain="",
            cookies_html="", cookies_plain="",
            error=str(e)
        )

