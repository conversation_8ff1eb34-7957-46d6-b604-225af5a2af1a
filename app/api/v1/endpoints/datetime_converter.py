from fastapi import APIRouter, Body
from pydantic import BaseModel
from app.core.logic import datetime_converter

router = APIRouter()

class ConversionResponse(BaseModel):
    result: dict | str | None = None
    error: str | None = None

@router.post("/datetime/ts-to-date", response_model=ConversionResponse)
async def to_datetime_endpoint(payload: dict = Body(...)):
    result, error = datetime_converter.convert_timestamp_to_datetime(payload.get("timestamp_str", ""))
    return ConversionResponse(result=result, error=error)

@router.post("/datetime/date-to-ts", response_model=ConversionResponse)
async def from_datetime_endpoint(payload: dict = Body(...)):
    result, error = datetime_converter.convert_datetime_to_timestamp(payload.get("datetime_str", ""))
    return ConversionResponse(result=result, error=error)

@router.post("/datetime/diff", response_model=ConversionResponse)
async def diff_endpoint(payload: dict = Body(...)):
    result, error = datetime_converter.calculate_date_difference(payload.get("start_date", ""), payload.get("end_date", ""))
    return ConversionResponse(result=result, error=error)

@router.post("/datetime/offset", response_model=ConversionResponse)
async def offset_endpoint(payload: dict = Body(...)):
    result, error = datetime_converter.calculate_date_offset(payload.get("start_date", ""), payload.get("days", 0))
    return ConversionResponse(result=result, error=error)