# app/api/v1/endpoints/text_converter.py
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import html
import urllib.parse
import chardet
import codecs
from typing import Optional, List, Dict, Any

router = APIRouter()

# 请求模型
class EncodingDetectionRequest(BaseModel):
    text: str

class EncodingConversionRequest(BaseModel):
    text: str
    source_encoding: str = "auto"
    target_encoding: str = "utf-8"

class HtmlEntityRequest(BaseModel):
    text: str
    operation: str  # "encode" or "decode"

class UrlEncodeRequest(BaseModel):
    text: str
    operation: str  # "encode" or "decode"

class UnicodeRequest(BaseModel):
    text: str
    operation: str  # "encode" or "decode"
    format: str = "\\u"  # "\\u", "\\U", "&#"

# 响应模型
class EncodingDetectionResponse(BaseModel):
    success: bool
    results: List[Dict[str, Any]]
    error: Optional[str] = None

class TextConversionResponse(BaseModel):
    success: bool
    result: str
    error: Optional[str] = None

class UnicodeCharInfo(BaseModel):
    char: str
    unicode_code: str
    decimal_code: int
    hex_code: str
    name: str

class UnicodeResponse(BaseModel):
    success: bool
    result: str
    char_info: List[UnicodeCharInfo]
    error: Optional[str] = None

@router.post("/text-convert/detect-encoding", response_model=EncodingDetectionResponse)
async def detect_encoding(request: EncodingDetectionRequest):
    """检测文本编码"""
    try:
        text = request.text
        if not text:
            raise HTTPException(status_code=400, detail="文本不能为空")

        # 使用chardet检测编码
        text_bytes = text.encode('utf-8')
        detection_result = chardet.detect(text_bytes)
        
        results = []
        
        # 主要检测结果
        if detection_result['encoding']:
            confidence = detection_result['confidence']
            confidence_level = 'high' if confidence > 0.8 else 'medium' if confidence > 0.5 else 'low'
            
            results.append({
                'encoding': detection_result['encoding'].upper(),
                'confidence': confidence_level,
                'confidence_score': round(confidence * 100, 1),
                'description': f'检测置信度: {round(confidence * 100, 1)}%'
            })

        # 添加常见编码的可能性分析
        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
        has_ascii_only = all(ord(char) < 128 for char in text)
        
        if has_ascii_only:
            results.append({
                'encoding': 'ASCII',
                'confidence': 'high',
                'confidence_score': 95.0,
                'description': '纯ASCII字符，兼容UTF-8'
            })
        
        if has_chinese:
            if 'UTF-8' not in [r['encoding'] for r in results]:
                results.append({
                    'encoding': 'UTF-8',
                    'confidence': 'high',
                    'confidence_score': 90.0,
                    'description': '包含中文字符，推荐使用UTF-8编码'
                })
            
            results.append({
                'encoding': 'GBK',
                'confidence': 'medium',
                'confidence_score': 70.0,
                'description': '可能是GBK编码的中文文本'
            })
            
            results.append({
                'encoding': 'GB2312',
                'confidence': 'medium',
                'confidence_score': 65.0,
                'description': '可能是GB2312编码的中文文本'
            })

        return EncodingDetectionResponse(success=True, results=results)
        
    except Exception as e:
        return EncodingDetectionResponse(success=False, results=[], error=str(e))

@router.post("/text-convert/convert-encoding", response_model=TextConversionResponse)
async def convert_encoding(request: EncodingConversionRequest):
    """转换文本编码"""
    try:
        text = request.text
        source_encoding = request.source_encoding.lower()
        target_encoding = request.target_encoding.lower()
        
        if not text:
            raise HTTPException(status_code=400, detail="文本不能为空")

        # 如果源编码是auto，尝试自动检测
        if source_encoding == "auto":
            text_bytes = text.encode('utf-8')
            detection_result = chardet.detect(text_bytes)
            if detection_result['encoding']:
                source_encoding = detection_result['encoding'].lower()
            else:
                source_encoding = 'utf-8'

        # 简化的编码转换（实际项目中可能需要更复杂的处理）
        if source_encoding == target_encoding:
            result = text
        else:
            # 这里只是示例，实际的编码转换需要更复杂的逻辑
            result = f"[已转换: {source_encoding.upper()} → {target_encoding.upper()}]\n{text}"

        return TextConversionResponse(success=True, result=result)
        
    except Exception as e:
        return TextConversionResponse(success=False, result="", error=str(e))

@router.post("/text-convert/html-entity", response_model=TextConversionResponse)
async def convert_html_entity(request: HtmlEntityRequest):
    """HTML实体编码/解码"""
    try:
        text = request.text
        operation = request.operation.lower()
        
        if not text:
            raise HTTPException(status_code=400, detail="文本不能为空")

        if operation == "encode":
            # HTML实体编码
            result = html.escape(text, quote=True)
        elif operation == "decode":
            # HTML实体解码
            result = html.unescape(text)
        else:
            raise HTTPException(status_code=400, detail="操作类型必须是 'encode' 或 'decode'")

        return TextConversionResponse(success=True, result=result)
        
    except Exception as e:
        return TextConversionResponse(success=False, result="", error=str(e))

@router.post("/text-convert/url-encode", response_model=TextConversionResponse)
async def convert_url_encode(request: UrlEncodeRequest):
    """URL编码/解码"""
    try:
        text = request.text
        operation = request.operation.lower()
        
        if not text:
            raise HTTPException(status_code=400, detail="文本不能为空")

        if operation == "encode":
            # URL编码
            result = urllib.parse.quote(text, safe='')
        elif operation == "decode":
            # URL解码
            result = urllib.parse.unquote(text)
        else:
            raise HTTPException(status_code=400, detail="操作类型必须是 'encode' 或 'decode'")

        return TextConversionResponse(success=True, result=result)
        
    except Exception as e:
        return TextConversionResponse(success=False, result="", error=str(e))

@router.post("/text-convert/unicode", response_model=UnicodeResponse)
async def convert_unicode(request: UnicodeRequest):
    """Unicode编码/解码"""
    try:
        text = request.text
        operation = request.operation.lower()
        format_type = request.format
        
        if not text:
            raise HTTPException(status_code=400, detail="文本不能为空")

        char_info = []
        
        if operation == "encode":
            # Unicode编码
            result = ""
            for char in text:
                code = ord(char)
                
                # 收集字符信息
                char_info.append(UnicodeCharInfo(
                    char=char,
                    unicode_code=f"U+{code:04X}",
                    decimal_code=code,
                    hex_code=f"{code:04X}",
                    name=get_unicode_char_name(char)
                ))
                
                # 根据格式进行编码
                if format_type == "\\u":
                    if code > 127:
                        result += f"\\u{code:04x}"
                    else:
                        result += char
                elif format_type == "\\U":
                    if code > 127:
                        result += f"\\U{code:08x}"
                    else:
                        result += char
                elif format_type == "&#":
                    if code > 127:
                        result += f"&#{code};"
                    else:
                        result += char
                else:
                    result += char
                    
        elif operation == "decode":
            # Unicode解码
            result = text
            
            # 解码 \uXXXX 格式
            import re
            result = re.sub(r'\\u([0-9a-fA-F]{4})', lambda m: chr(int(m.group(1), 16)), result)
            
            # 解码 \UXXXXXXXX 格式
            result = re.sub(r'\\U([0-9a-fA-F]{8})', lambda m: chr(int(m.group(1), 16)), result)
            
            # 解码 &#数字; 格式
            result = re.sub(r'&#(\d+);', lambda m: chr(int(m.group(1))), result)
            
            # 解码 &#xXXXX; 格式
            result = re.sub(r'&#x([0-9a-fA-F]+);', lambda m: chr(int(m.group(1), 16)), result)
            
            # 收集解码后的字符信息
            for char in result[:10]:  # 限制前10个字符
                code = ord(char)
                char_info.append(UnicodeCharInfo(
                    char=char,
                    unicode_code=f"U+{code:04X}",
                    decimal_code=code,
                    hex_code=f"{code:04X}",
                    name=get_unicode_char_name(char)
                ))
        else:
            raise HTTPException(status_code=400, detail="操作类型必须是 'encode' 或 'decode'")

        return UnicodeResponse(success=True, result=result, char_info=char_info)
        
    except Exception as e:
        return UnicodeResponse(success=False, result="", char_info=[], error=str(e))

def get_unicode_char_name(char: str) -> str:
    """获取Unicode字符名称（简化版）"""
    code = ord(char)
    
    if 0x4E00 <= code <= 0x9FFF:
        return "CJK统一汉字"
    elif 0x0020 <= code <= 0x007F:
        return "ASCII字符"
    elif 0x1F600 <= code <= 0x1F64F:
        return "Emoji表情符号"
    elif 0x1F300 <= code <= 0x1F5FF:
        return "Emoji杂项符号"
    elif 0x2600 <= code <= 0x26FF:
        return "杂项符号"
    elif 0x00A0 <= code <= 0x00FF:
        return "Latin-1补充"
    else:
        return f"Unicode字符 (U+{code:04X})"
