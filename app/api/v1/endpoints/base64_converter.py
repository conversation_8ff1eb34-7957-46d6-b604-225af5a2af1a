# 文件路径: app/api/v1/endpoints/base64_converter.py

from fastapi import APIRouter
from pydantic import BaseModel
from app.core.logic import base64_converter

router = APIRouter()


class Base64EncodeRequest(BaseModel):
    plain_text: str


class Base64DecodeRequest(BaseModel):
    b64_string: str


class Base64Response(BaseModel):
    result: str | None = None
    error: str | None = None


@router.post("/base64/encode", response_model=Base64Response)
async def encode_endpoint(request: Base64EncodeRequest):
    result, error = base64_converter.encode_to_base64(request.plain_text)
    return Base64Response(result=result, error=error)


@router.post("/base64/decode", response_model=Base64Response)
async def decode_endpoint(request: Base64DecodeRequest):
    result, error = base64_converter.decode_from_base64(request.b64_string)
    return Base64Response(result=result, error=error)