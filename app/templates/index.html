<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>爬虫工具箱</title>
    <!-- 依赖库 -->
     <style>
        .search-highlight {
            background-color: yellow;
            color: black;
        }
        .current-match {
            background-color: orange !important;
            border: 1px solid red;
            border-radius: 2px;
        }

        /* 文本对比工具样式 */
        .diff-line {
            padding: 2px 8px;
            margin: 1px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .diff-line.equal {
            background-color: transparent;
        }
        .diff-line.deleted {
            background-color: #fee2e2;
            color: #dc2626;
            border-left: 4px solid #dc2626;
        }
        .diff-line.added {
            background-color: #dcfce7;
            color: #16a34a;
            border-left: 4px solid #16a34a;
        }
        .line-number {
            display: inline-block;
            width: 40px;
            text-align: right;
            margin-right: 8px;
            color: #9ca3af;
            user-select: none;
        }

        /* JSON格式化器样式 - 紧凑现代风格 */
        .json-line {
            line-height: 1.4;
            font-family: 'Courier New', 'Monaco', monospace;
            font-size: 13px;
            white-space: pre;
        }

        .json-toggle {
            cursor: pointer;
            user-select: none;
            margin-right: 4px;
        }

        .json-toggle:hover .json-toggle-icon {
            color: #3b82f6 !important;
        }

        .json-toggle-icon {
            color: #9ca3af;
            font-size: 12px;
            transition: color 0.2s;
        }

        .json-bracket {
            color: #6b7280;
            font-weight: bold;
        }

        .json-key {
            color: #059669;
            font-weight: 500;
        }

        .json-string {
            color: #dc2626;
        }

        .json-number {
            color: #2563eb;
            font-weight: 500;
        }

        .json-boolean {
            color: #7c3aed;
            font-weight: 500;
        }

        .json-null {
            color: #7c3aed;
            font-weight: 500;
            font-style: italic;
        }

        .json-count {
            font-size: 11px;
            color: #9ca3af;
            font-weight: normal;
        }

        .json-content {
            transition: all 0.2s ease;
        }

        .json-content.collapsed {
            display: none;
        }

        /* 文本对比工具现代化样式 */
        .text-compare-dark {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: #e2e8f0;
        }

        .text-compare-dark .bg-white {
            background-color: #334155 !important;
            color: #e2e8f0;
        }

        .text-compare-dark .bg-gray-50 {
            background-color: #475569 !important;
            color: #e2e8f0;
        }

        .text-compare-dark .border-gray-200 {
            border-color: #64748b !important;
        }

        .text-compare-dark textarea {
            background-color: transparent !important;
            color: #e2e8f0;
        }

        .text-compare-dark textarea::placeholder {
            color: #94a3b8;
        }

        /* 实时对比开关样式 */
        #real-time-compare:checked + div .bg-gray-300 {
            background-color: #3b82f6;
        }

        #real-time-compare:checked + div .left-1 {
            transform: translateX(1rem);
        }

        /* 可拖拽分隔线样式 */
        #resizer:hover {
            width: 4px;
        }

        /* 差异高亮样式 */
        .diff-line {
            padding: 2px 8px;
            margin: 1px 0;
            border-radius: 4px;
        }

        .diff-line.added {
            background-color: #dcfce7;
            border-left: 4px solid #22c55e;
            color: #166534;
        }

        .diff-line.removed {
            background-color: #fef2f2;
            border-left: 4px solid #ef4444;
            color: #991b1b;
        }

        .diff-line.modified {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            color: #92400e;
        }

        .diff-line.unchanged {
            background-color: #f8fafc;
            color: #64748b;
        }

        /* 暗色主题差异样式 */
        .text-compare-dark .diff-line.added {
            background-color: #064e3b;
            color: #6ee7b7;
        }

        .text-compare-dark .diff-line.removed {
            background-color: #7f1d1d;
            color: #fca5a5;
        }

        .text-compare-dark .diff-line.modified {
            background-color: #78350f;
            color: #fcd34d;
        }

        .text-compare-dark .diff-line.unchanged {
            background-color: #475569;
            color: #94a3b8;
        }

        /* 搜索高亮 */
        .search-highlight {
            background-color: #fbbf24;
            color: #92400e;
            padding: 1px 2px;
            border-radius: 2px;
        }

        .search-highlight.current {
            background-color: #f59e0b;
            color: white;
        }

        /* 导航侧边栏样式 */
        .diff-nav-item {
            width: 12px;
            height: 8px;
            margin: 2px auto;
            border-radius: 2px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .diff-nav-item.added {
            background-color: #22c55e;
        }

        .diff-nav-item.removed {
            background-color: #ef4444;
        }

        .diff-nav-item.modified {
            background-color: #f59e0b;
        }

        .diff-nav-item:hover {
            transform: scale(1.2);
        }

        /* 全屏模式样式 */
        .fullscreen-mode {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            background: white;
        }

        .fullscreen-mode.text-compare-dark {
            background: #1e293b;
        }

        /* 行号同步滚动 */
        .line-numbers-container {
            overflow: hidden;
        }

        /* 动画效果 */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-slide-in {
            animation: slideIn 0.3s ease-out;
        }

        /* 文本转换工具选项卡样式 */
        .text-convert-tab-btn {
            border-bottom: 2px solid transparent;
            color: #6b7280;
            transition: all 0.2s;
            cursor: pointer;
        }

        .text-convert-tab-btn:hover {
            color: #374151;
            background-color: #f3f4f6;
        }

        .text-convert-tab-btn.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
            background-color: #eff6ff;
        }

        .text-convert-tab-content {
            display: block;
        }

        .text-convert-tab-content.tab-hidden {
            display: none;
        }

        /* 编码检测结果样式 */
        .encoding-info {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-left: 4px solid #0ea5e9;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .encoding-confidence {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .confidence-high {
            background-color: #dcfce7;
            color: #166534;
        }

        .confidence-medium {
            background-color: #fef3c7;
            color: #92400e;
        }

        .confidence-low {
            background-color: #fee2e2;
            color: #991b1b;
        }

        /* Unicode字符信息样式 */
        .unicode-char-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 8px;
            margin-bottom: 6px;
            font-family: 'Courier New', monospace;
        }

        .unicode-char-item .char {
            font-size: 18px;
            font-weight: bold;
            color: #7c3aed;
        }

        .unicode-char-item .code {
            color: #059669;
            font-size: 12px;
        }

        .unicode-char-item .name {
            color: #6b7280;
            font-size: 11px;
        }
    </style>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <!-- 外部自定义样式 -->
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body class="bg-gray-100 font-sans flex h-screen antialiased">

    <!-- 侧边导航栏 -->
    <aside class="w-60 bg-white shadow-md flex flex-col flex-shrink-0">
        <div class="p-4 border-b"><h1 class="text-xl font-bold text-gray-800">爬虫工具箱</h1></div>
        <nav id="sidebar-nav" class="flex-1 p-2 space-y-1">
            <a href="#" data-tool="json-tool" class="sidebar-link active block px-4 py-2 text-sm rounded-md text-gray-700">JSON格式化</a>
            <a href="#" data-tool="headers-tool" class="sidebar-link block px-4 py-2 text-sm rounded-md text-gray-700">Headers格式化</a>
            <a href="#" data-tool="xml-tool" class="sidebar-link block px-4 py-2 text-sm rounded-md text-gray-700">XML格式化</a>
            <a href="#" data-tool="html-format-tool" class="sidebar-link block px-4 py-2 text-sm rounded-md text-gray-700">HTML格式化</a>
            <a href="#" data-tool="html-render-tool" class="sidebar-link block px-4 py-2 text-sm rounded-md text-gray-700">HTML渲染</a>
            <a href="#" data-tool="url-tool" class="sidebar-link block px-4 py-2 text-sm rounded-md text-gray-700">URL参数提取</a>
            <a href="#" data-tool="base64-tool" class="sidebar-link block px-4 py-2 text-sm rounded-md text-gray-700">Base64编码解码</a>
            <a href="#" data-tool="datetime-tool" class="sidebar-link block px-4 py-2 text-sm rounded-md text-gray-700">日期时间转换</a>
            <a href="#" data-tool="text-compare-tool" class="sidebar-link block px-4 py-2 text-sm rounded-md text-gray-700">文本对比</a>
            <a href="#" data-tool="text-convert-tool" class="sidebar-link block px-4 py-2 text-sm rounded-md text-gray-700">文本转换</a>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <div class="flex-1 flex flex-col overflow-hidden">
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-4 md:p-8">

            <!-- JSON 格式化工具 -->
            <div id="json-tool" class="tool-content flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">JSON 格式化工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="format-json-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-magic w-4 h-4 mr-1"></i>格式化
                                </button>
                                <button id="clear-json-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-json" class="tool-btn text-blue-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">常规示例</button>
                                <button id="load-sortable-json" class="tool-btn text-green-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">排序示例</button>
                            </div>
                        </div>
                        <textarea id="raw-json" class="flex-1 w-full p-3 border-l border-r border-b bg-gray-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此粘贴您的JSON数据..."></textarea>
                        <div id="json-input-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>
                    <div class="w-1/2 flex flex-col">
                        <div id="json-toolbar" class="flex items-center space-x-3 bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                           <button data-action="search" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-search w-4 h-4 mr-1"></i>搜索</button>
                           <button data-action="collapse" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-compress-arrows-alt w-4 h-4 mr-1"></i>折叠/展开</button>
                           <button data-action="escape" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-exchange-alt w-4 h-4 mr-1"></i>转义</button>
                           <button data-action="sort" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-sort-alpha-down w-4 h-4 mr-1"></i>排序</button>
                           <button data-action="compress" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-align-left w-4 h-4 mr-1"></i>压缩</button>
                           <button data-action="copy" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-copy w-4 h-4 mr-1"></i>复制</button>
                        </div>

                    <div class="search-bar-container hidden items-center space-x-2 bg-gray-200 p-1 border-b border-gray-300">
                            <input type="text" placeholder="搜索..." class="search-input flex-grow p-1 rounded-sm border-gray-300 focus:ring-blue-500 focus:border-blue-500 text-sm">
                            <span class="search-count text-sm text-gray-700 font-mono">0 / 0</span>
                            <div class="flex items-center">
                                <button class="search-prev-btn p-1 hover:bg-gray-300 rounded-sm disabled:opacity-50" title="上一个"><i class="fas fa-arrow-up fa-fw"></i></button>
                                <button class="search-next-btn p-1 hover:bg-gray-300 rounded-sm disabled:opacity-50" title="下一个"><i class="fas fa-arrow-down fa-fw"></i></button>
                            </div>
                            <button class="search-close-btn p-1 hover:bg-gray-300 rounded-sm" title="关闭"><i class="fas fa-times fa-fw"></i></button>
                        </div>



                        <div class="relative flex-1 overflow-hidden">
                            <pre><code id="json-output" data-plain-text="" class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r border-b rounded-b-lg font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                        </div>
                        <div id="json-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>

            <!-- Headers 格式化工具 -->
            <div id="headers-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">请求头 Headers 格式化工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: 输入区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="convert-headers-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-magic w-4 h-4 mr-1"></i>转换格式
                                </button>
                                <button id="clear-headers-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-headers" class="tool-btn text-blue-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">载入示例</button>
                            </div>
                        </div>
                        <textarea id="raw-headers" class="flex-1 w-full p-3 border-l border-r border-b bg-gray-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此粘贴从浏览器复制的原始Headers..."></textarea>
                        <div id="headers-input-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>

                    <!-- 右侧: 双输出区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 右侧工具栏 -->
                        <div class="flex items-center space-x-3 bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <button data-action="copy-headers" class="tool-btn hover:bg-gray-200 p-1 rounded">
                                <i class="fas fa-copy w-4 h-4 mr-1"></i>复制Headers
                            </button>
                            <button data-action="copy-cookies" class="tool-btn hover:bg-gray-200 p-1 rounded">
                                <i class="fas fa-cookie-bite w-4 h-4 mr-1"></i>复制Cookies
                            </button>
                        </div>

                        <!-- Headers输出区 (60%高度) -->
                        <div class="flex-1 flex flex-col" style="flex: 3;">
                            <div class="bg-gray-50 px-3 py-1 border-l border-r text-xs text-gray-600 font-medium">
                                Python Headers 字典:
                            </div>
                            <div class="relative flex-1 overflow-hidden">
                                <pre><code id="headers-output" data-plain-text="" class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                            </div>
                        </div>

                        <!-- Cookies输出区 (40%高度) -->
                        <div class="flex-1 flex flex-col" style="flex: 2;">
                            <div class="bg-gray-50 px-3 py-1 border-l border-r border-t text-xs text-gray-600 font-medium">
                                Python Cookies 字典:
                            </div>
                            <div class="relative flex-1 overflow-hidden">
                                <pre><code id="cookies-output" data-plain-text="" class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r border-b rounded-b-lg font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                            </div>
                        </div>

                        <div id="headers-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>

            <!-- XML 格式化工具 -->
            <div id="xml-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">XML 格式化工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: 输入区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="format-xml-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-magic w-4 h-4 mr-1"></i>格式化
                                </button>
                                <button id="clear-xml-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-xml" class="tool-btn text-blue-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">载入示例</button>
                            </div>
                        </div>
                        <textarea id="raw-xml" class="flex-1 w-full p-3 border-l border-r border-b bg-gray-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此粘贴您的XML数据..."></textarea>
                        <div id="xml-input-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>
                    <!-- 右侧: 输出区 -->
                    <div class="w-1/2 flex flex-col">
                        <div id="xml-toolbar" class="flex items-center space-x-3 bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                           <button data-action="search" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-search w-4 h-4 mr-1"></i>搜索</button>
                           <button data-action="compress" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-align-left w-4 h-4 mr-1"></i>压缩</button>
                           <button data-action="copy" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-copy w-4 h-4 mr-1"></i>复制</button>
                        </div>

                        <div class="search-bar-container hidden items-center space-x-2 bg-gray-200 p-1 border-b border-gray-300">
                            <input type="text" placeholder="搜索..." class="search-input flex-grow p-1 rounded-sm border-gray-300 focus:ring-blue-500 focus:border-blue-500 text-sm">
                            <span class="search-count text-sm text-gray-700 font-mono">0 / 0</span>
                            <div class="flex items-center">
                                <button class="search-prev-btn p-1 hover:bg-gray-300 rounded-sm disabled:opacity-50" title="上一个"><i class="fas fa-arrow-up fa-fw"></i></button>
                                <button class="search-next-btn p-1 hover:bg-gray-300 rounded-sm disabled:opacity-50" title="下一个"><i class="fas fa-arrow-down fa-fw"></i></button>
                            </div>
                            <button class="search-close-btn p-1 hover:bg-gray-300 rounded-sm" title="关闭"><i class="fas fa-times fa-fw"></i></button>
                        </div>


                        <div class="relative flex-1 overflow-hidden">
                            <pre><code id="xml-output" data-plain-text="" class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r border-b rounded-b-lg font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                        </div>
                        <div id="xml-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>


            <div id="html-format-tool"
                 class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">HTML 格式化工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="format-html-button" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-magic w-4 h-4 mr-1"></i>格式化
                                </button>
                                <button id="clear-html-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-html-btn" class="tool-btn text-blue-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">载入示例</button>
                            </div>
                        </div>
                        <textarea id="raw-html-input"
                                  class="flex-1 w-full p-3 border-l border-r border-b bg-gray-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm"
                                  placeholder="在此粘贴您的HTML数据..."></textarea>
                        <div id="html-input-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>


                    <div class="w-1/2 flex flex-col">
                        <div id="html-toolbar"
                             class="flex items-center space-x-3 bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">

                             <button data-action="search" class="tool-btn hover:bg-gray-200 p-1 rounded"><i
                                    class="fas fa-search w-4 h-4 mr-1"></i>搜索
                            </button>

                            <button data-action="copy" class="tool-btn hover:bg-gray-200 p-1 rounded"><i
                                    class="fas fa-copy w-4 h-4 mr-1"></i>复制
                            </button>
                        </div>


                          <div class="search-bar-container hidden items-center space-x-2 bg-gray-200 p-1 border-b border-gray-300">
                            <input type="text" placeholder="搜索..." class="search-input flex-grow p-1 rounded-sm border-gray-300 focus:ring-blue-500 focus:border-blue-500 text-sm">
                            <span class="search-count text-sm text-gray-700 font-mono">0 / 0</span>
                            <div class="flex items-center">
                                <button class="search-prev-btn p-1 hover:bg-gray-300 rounded-sm disabled:opacity-50" title="上一个"><i class="fas fa-arrow-up fa-fw"></i></button>
                                <button class="search-next-btn p-1 hover:bg-gray-300 rounded-sm disabled:opacity-50" title="下一个"><i class="fas fa-arrow-down fa-fw"></i></button>
                            </div>
                            <button class="search-close-btn p-1 hover:bg-gray-300 rounded-sm" title="关闭"><i class="fas fa-times fa-fw"></i></button>
                        </div>


                        <div class="relative flex-1 overflow-hidden">
                            <pre><code id="html-output" data-plain-text=""
                                       class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r border-b rounded-b-lg font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                        </div>
                        <div id="html-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>

           <div id="html-render-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">HTML 渲染工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: HTML输入区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="render-html-btn" class="tool-btn bg-green-600 text-white hover:bg-green-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-play w-4 h-4 mr-1"></i>渲染HTML
                                </button>
                                <button id="clear-html-render-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-render-btn" class="tool-btn text-blue-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">载入示例</button>
                            </div>
                        </div>
                        <textarea id="html-render-input" class="flex-1 w-full p-3 border-l border-r border-b bg-gray-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此粘贴HTML源代码..."></textarea>
                        <div id="html-render-input-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>

                    <!-- 右侧: 渲染结果区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 右侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <span class="font-medium">渲染结果</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-500">安全沙箱环境</span>
                                <button id="refresh-render-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-sync-alt w-3 h-3 mr-1"></i>刷新
                                </button>
                            </div>
                        </div>
                        <div class="flex-1 border-l border-r border-b bg-white rounded-b-lg overflow-hidden">
                            <iframe id="html-render-output" class="w-full h-full bg-white" sandbox="allow-same-origin"></iframe>
                        </div>
                        <div id="html-render-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>

            <div id="url-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">URL 参数提取工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: 输入区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="parse-url-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-magic w-4 h-4 mr-1"></i>提取参数
                                </button>
                                <button id="clear-url-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-url-btn" class="tool-btn text-blue-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">载入示例</button>
                            </div>
                        </div>
                        <textarea id="raw-url-input" class="flex-1 w-full p-3 border-l border-r border-b bg-gray-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此粘贴完整的URL链接..."></textarea>
                        <div id="url-input-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>

                    <!-- 右侧: 双输出区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 右侧工具栏 -->
                        <div class="flex items-center space-x-3 bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <button data-action="copy-base-url" class="tool-btn hover:bg-gray-200 p-1 rounded">
                                <i class="fas fa-link w-4 h-4 mr-1"></i>复制URL
                            </button>
                            <button data-action="copy-params" class="tool-btn hover:bg-gray-200 p-1 rounded">
                                <i class="fas fa-list w-4 h-4 mr-1"></i>复制参数
                            </button>
                        </div>

                        <!-- 基础URL输出区 (30%高度) -->
                        <div class="flex-1 flex flex-col" style="flex: 1.5;">
                            <div class="bg-gray-50 px-3 py-1 border-l border-r text-xs text-gray-600 font-medium">
                                基础 URL:
                            </div>
                            <div class="relative flex-1 overflow-hidden">
                                <pre><code id="base-url-output" data-plain-text="" class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                            </div>
                        </div>

                        <!-- 参数输出区 (70%高度) -->
                        <div class="flex-1 flex flex-col" style="flex: 3.5;">
                            <div class="bg-gray-50 px-3 py-1 border-l border-r border-t text-xs text-gray-600 font-medium">
                                参数 (Params) 字典:
                            </div>
                            <div class="relative flex-1 overflow-hidden">
                                <pre><code id="params-output" data-plain-text="" class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r border-b rounded-b-lg font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                            </div>
                        </div>

                        <div id="url-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>

            <div id="base64-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">Base64 编码解码工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: 明文输入区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-blue-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="base64-encode-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-arrow-down w-4 h-4 mr-1"></i>编码
                                </button>
                                <button id="clear-base64-plain-btn" class="tool-btn hover:bg-blue-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="copy-base64-plain-btn" class="tool-btn text-blue-600 hover:bg-blue-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-copy w-3 h-3 mr-1"></i>复制
                                </button>
                            </div>
                        </div>
                        <div class="bg-blue-50 px-3 py-1 border-l border-r text-xs text-blue-700 font-medium">
                            明文 (Plain Text):
                        </div>
                        <textarea id="base64-plain-input" class="flex-1 w-full p-3 border-l border-r border-b bg-blue-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此输入要编码的明文..."></textarea>
                        <div id="base64-plain-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>

                    <!-- 右侧: Base64输出区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 右侧工具栏 -->
                        <div class="flex items-center justify-between bg-green-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="base64-decode-btn" class="tool-btn bg-green-600 text-white hover:bg-green-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-arrow-up w-4 h-4 mr-1"></i>解码
                                </button>
                                <button id="clear-base64-encoded-btn" class="tool-btn hover:bg-green-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="copy-base64-encoded-btn" class="tool-btn text-green-600 hover:bg-green-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-copy w-3 h-3 mr-1"></i>复制
                                </button>
                            </div>
                        </div>
                        <div class="bg-green-50 px-3 py-1 border-l border-r text-xs text-green-700 font-medium">
                            Base64 字符串:
                        </div>
                        <textarea id="base64-b64-input" class="flex-1 w-full p-3 border-l border-r border-b bg-green-50 rounded-b-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此输入要解码的Base64字符串..."></textarea>
                        <div id="base64-encoded-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>
                </div>
                <div id="base64-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
            </div>

       <div id="datetime-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-4">日期时间转换工具</h2>

                <div class="flex border-b mb-4 flex-shrink-0">
                    <button data-tab="datetime-tab-1" class="datetime-tab-btn py-2 px-4 text-sm font-medium text-center active">时间戳 &harr; 日期</button>
                    <button data-tab="datetime-tab-2" class="datetime-tab-btn py-2 px-4 text-sm font-medium text-center">日期计算器</button>
                </div>
                <div id="datetime-message-area" class="text-center text-sm text-red-600 h-5 mb-2 flex-shrink-0"></div>

                <div class="flex-1 overflow-y-auto relative">
                    <div id="datetime-tab-1" class="datetime-tab-content">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8">
                            <div class="flex flex-col space-y-4">
                                <h3 class="text-lg font-medium text-gray-800">时间戳 &rarr; 日期时间</h3>
                                <div>
                                    <label for="ts-to-date-input" class="block text-sm font-medium text-gray-600 mb-1">输入时间戳:</label>
                                    <input type="text" id="ts-to-date-input" class="w-full p-2 border border-gray-300 rounded-lg font-mono text-sm">
                                </div>
                                <button id="ts-to-date-btn" class="w-full bg-blue-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-blue-700">转换 &darr;</button>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">转换结果:</label>
                                    <div id="ts-to-date-output" class="w-full p-3 bg-gray-100 border rounded-lg text-sm min-h-[120px] whitespace-pre-wrap font-mono"></div>
                                </div>
                            </div>
                            <div class="flex flex-col space-y-4 mt-8 md:mt-0">
                                <h3 class="text-lg font-medium text-gray-800">日期时间 &rarr; 时间戳</h3>
                                <div>
                                    <label for="date-to-ts-input" class="block text-sm font-medium text-gray-600 mb-1">输入日期时间:</label>
                                    <input type="text" id="date-to-ts-input" class="w-full p-2 border border-gray-300 rounded-lg font-mono text-sm">
                                </div>
                                <button id="date-to-ts-btn" class="w-full bg-green-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-green-700">转换 &uarr;</button>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">转换结果:</label>
                                    <div id="date-to-ts-output" class="w-full p-3 bg-gray-100 border rounded-lg text-sm min-h-[120px] whitespace-pre-wrap font-mono"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="datetime-tab-2" class="datetime-tab-content tab-hidden">
                        <div class="flex flex-col space-y-4 max-w-2xl mx-auto">
                            <div class="p-4 border rounded-lg">
                                <h3 class="text-lg font-medium text-gray-800">日期差计算器</h3>
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 items-end mt-2">
                                    <div>
                                       <label for="diff-start-date" class="block text-sm font-medium text-gray-600 mb-1">起始日期:</label>
                                       <input type="text" id="diff-start-date" class="w-full p-2 border border-gray-300 rounded-lg font-mono text-sm" value="2020-01-01 12:00:00">
                                    </div>
                                     <div>
                                       <label for="diff-end-date" class="block text-sm font-medium text-gray-600 mb-1">结束日期 (留空为当前):</label>
                                       <input type="text" id="diff-end-date" class="w-full p-2 border border-gray-300 rounded-lg font-mono text-sm" placeholder="YYYY-MM-DD HH:MM:SS">
                                     </div>
                                </div>
                                <button id="date-diff-btn" class="mt-4 w-full bg-indigo-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-indigo-700">计算差值</button>
                                <div id="date-diff-output" class="mt-2 w-full p-3 bg-gray-100 border rounded-lg text-sm min-h-[50px] whitespace-pre-wrap font-mono"></div>
                            </div>

                            <div class="p-4 border rounded-lg mt-6">
                                <h3 class="text-lg font-medium text-gray-800">日期偏移计算器</h3>
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 items-end mt-2">
                                    <div>
                                       <label for="offset-start-date" class="block text-sm font-medium text-gray-600 mb-1">起始日期:</label>
                                       <input type="text" id="offset-start-date" class="w-full p-2 border border-gray-300 rounded-lg font-mono text-sm">
                                    </div>
                                     <div>
                                       <label for="offset-days" class="block text-sm font-medium text-gray-600 mb-1">偏移天数 (负数为过去):</label>
                                       <input type="number" id="offset-days" class="w-full p-2 border border-gray-300 rounded-lg font-mono text-sm" value="195">
                                     </div>
                                </div>
                                <button id="date-offset-btn" class="mt-4 w-full bg-pink-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-pink-700">计算新日期</button>
                                <div id="date-offset-output" class="mt-2 w-full p-3 bg-gray-100 border rounded-lg text-sm min-h-[50px] whitespace-pre-wrap font-mono"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 文本对比工具 -->
            <div id="text-compare-tool" class="tool-content hidden flex flex-col h-full bg-gradient-to-br from-blue-50 to-indigo-100 p-6 rounded-xl shadow-lg">
                <!-- 标题栏 -->
                <div class="flex items-center justify-between mb-6 flex-shrink-0">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-code-branch text-white text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-800">文本对比工具</h2>
                            <p class="text-sm text-gray-600">智能文本差异分析</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <!-- 主题切换 -->
                        <button id="theme-toggle-btn" class="p-2 rounded-lg bg-white/50 hover:bg-white/70 transition-all duration-200 shadow-sm">
                            <i class="fas fa-moon text-gray-600"></i>
                        </button>
                        <!-- 全屏模式 -->
                        <button id="fullscreen-btn" class="p-2 rounded-lg bg-white/50 hover:bg-white/70 transition-all duration-200 shadow-sm">
                            <i class="fas fa-expand text-gray-600"></i>
                        </button>
                        <!-- 帮助 -->
                        <button id="help-btn" class="p-2 rounded-lg bg-white/50 hover:bg-white/70 transition-all duration-200 shadow-sm">
                            <i class="fas fa-question-circle text-gray-600"></i>
                        </button>
                    </div>
                </div>

                <!-- 高级工具栏 -->
                <div class="bg-white/70 backdrop-blur-sm rounded-xl p-4 mb-6 shadow-sm flex-shrink-0">
                    <div class="flex items-center justify-between flex-wrap gap-4">
                        <!-- 主要操作按钮 -->
                        <div class="flex items-center space-x-3">
                            <button id="compare-texts-btn" class="bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 px-6 py-2.5 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105">
                                <i class="fas fa-search mr-2"></i>开始对比
                            </button>
                            <button id="compare-a-to-b-btn" class="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700 px-4 py-2.5 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg">
                                <i class="fas fa-arrow-right mr-2"></i>A→B
                            </button>
                            <button id="compare-b-to-a-btn" class="bg-gradient-to-r from-orange-500 to-orange-600 text-white hover:from-orange-600 hover:to-orange-700 px-4 py-2.5 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg">
                                <i class="fas fa-arrow-left mr-2"></i>B→A
                            </button>
                            <button id="clear-all-texts-btn" class="bg-gradient-to-r from-gray-500 to-gray-600 text-white hover:from-gray-600 hover:to-gray-700 px-4 py-2.5 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg">
                                <i class="fas fa-eraser mr-2"></i>清空
                            </button>
                        </div>

                        <!-- 实时对比开关 -->
                        <div class="flex items-center space-x-2">
                            <label class="flex items-center text-sm text-gray-700 font-medium">
                                <input type="checkbox" id="real-time-compare" class="sr-only">
                                <div class="relative">
                                    <div class="w-10 h-6 bg-gray-300 rounded-full shadow-inner transition-colors duration-200"></div>
                                    <div class="absolute w-4 h-4 bg-white rounded-full shadow top-1 left-1 transition-transform duration-200"></div>
                                </div>
                                <span class="ml-2">实时对比</span>
                            </label>
                        </div>
                    </div>

                    <!-- 对比选项 -->
                    <div class="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                        <div class="flex items-center space-x-6">
                            <label class="flex items-center text-sm text-gray-700 cursor-pointer">
                                <input type="checkbox" id="ignore-whitespace" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                <span class="ml-2">忽略空白字符</span>
                            </label>
                            <label class="flex items-center text-sm text-gray-700 cursor-pointer">
                                <input type="checkbox" id="ignore-case" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                <span class="ml-2">忽略大小写</span>
                            </label>
                            <label class="flex items-center text-sm text-gray-700 cursor-pointer">
                                <input type="checkbox" id="ignore-empty-lines" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                <span class="ml-2">忽略空行</span>
                            </label>
                            <label class="flex items-center text-sm text-gray-700 cursor-pointer">
                                <input type="checkbox" id="show-line-numbers" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" checked>
                                <span class="ml-2">显示行号</span>
                            </label>
                        </div>

                        <!-- 撤销重做按钮 -->
                        <div class="flex items-center space-x-2">
                            <button id="undo-btn" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors duration-200" title="撤销 (Ctrl+Z)">
                                <i class="fas fa-undo text-gray-600"></i>
                            </button>
                            <button id="redo-btn" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors duration-200" title="重做 (Ctrl+Y)">
                                <i class="fas fa-redo text-gray-600"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <div class="flex-1 flex space-x-4 overflow-hidden">
                    <!-- 左侧文本A -->
                    <div class="w-1/2 flex flex-col bg-white rounded-xl shadow-lg overflow-hidden">
                        <div class="flex items-center justify-between bg-gradient-to-r from-red-500 to-pink-500 text-white p-3">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-file-alt"></i>
                                <span class="font-semibold">文本 A</span>
                                <span id="text-a-stats" class="text-xs bg-white/20 px-2 py-1 rounded">0 字符</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-text-a" class="text-xs bg-white/20 hover:bg-white/30 px-2 py-1 rounded transition-colors">
                                    <i class="fas fa-download mr-1"></i>示例
                                </button>
                                <button id="clear-text-a" class="text-xs bg-white/20 hover:bg-white/30 px-2 py-1 rounded transition-colors">
                                    <i class="fas fa-trash mr-1"></i>清空
                                </button>
                                <button id="copy-text-a" class="text-xs bg-white/20 hover:bg-white/30 px-2 py-1 rounded transition-colors">
                                    <i class="fas fa-copy mr-1"></i>复制
                                </button>
                            </div>
                        </div>
                        <div class="flex-1 relative">
                            <div id="text-a-line-numbers" class="absolute left-0 top-0 w-12 bg-gray-50 border-r border-gray-200 text-xs text-gray-500 font-mono leading-5 p-2 select-none overflow-hidden"></div>
                            <textarea id="text-a-input" class="w-full h-full pl-14 pr-3 py-2 border-0 focus:ring-0 resize-none font-mono text-sm leading-5 bg-transparent" placeholder="在此粘贴第一段文本..."></textarea>
                        </div>
                    </div>

                    <!-- 可拖拽分隔线 -->
                    <div id="resizer" class="w-1 bg-gradient-to-b from-blue-300 to-indigo-400 rounded-full cursor-col-resize hover:from-blue-400 hover:to-indigo-500 transition-all duration-200 shadow-sm"></div>

                    <!-- 右侧文本B -->
                    <div class="w-1/2 flex flex-col bg-white rounded-xl shadow-lg overflow-hidden">
                        <div class="flex items-center justify-between bg-gradient-to-r from-green-500 to-emerald-500 text-white p-3">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-file-alt"></i>
                                <span class="font-semibold">文本 B</span>
                                <span id="text-b-stats" class="text-xs bg-white/20 px-2 py-1 rounded">0 字符</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-text-b" class="text-xs bg-white/20 hover:bg-white/30 px-2 py-1 rounded transition-colors">
                                    <i class="fas fa-download mr-1"></i>示例
                                </button>
                                <button id="clear-text-b" class="text-xs bg-white/20 hover:bg-white/30 px-2 py-1 rounded transition-colors">
                                    <i class="fas fa-trash mr-1"></i>清空
                                </button>
                                <button id="copy-text-b" class="text-xs bg-white/20 hover:bg-white/30 px-2 py-1 rounded transition-colors">
                                    <i class="fas fa-copy mr-1"></i>复制
                                </button>
                            </div>
                        </div>
                        <div class="flex-1 relative">
                            <div id="text-b-line-numbers" class="absolute left-0 top-0 w-12 bg-gray-50 border-r border-gray-200 text-xs text-gray-500 font-mono leading-5 p-2 select-none overflow-hidden"></div>
                            <textarea id="text-b-input" class="w-full h-full pl-14 pr-3 py-2 border-0 focus:ring-0 resize-none font-mono text-sm leading-5 bg-transparent" placeholder="在此粘贴第二段文本..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- 对比结果区域 -->
                <div class="mt-6 flex-1 flex flex-col bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="flex items-center justify-between bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-3">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-chart-line"></i>
                            <span class="font-semibold">对比结果</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <!-- 搜索框 -->
                            <div class="relative">
                                <input type="text" id="search-input" class="bg-white/20 text-white placeholder-white/70 px-3 py-1 rounded text-sm w-40 focus:outline-none focus:bg-white/30" placeholder="搜索差异...">
                                <button id="search-prev" class="absolute right-8 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-white">
                                    <i class="fas fa-chevron-up text-xs"></i>
                                </button>
                                <button id="search-next" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-white">
                                    <i class="fas fa-chevron-down text-xs"></i>
                                </button>
                            </div>
                            <!-- 差异统计 -->
                            <div id="diff-stats" class="text-xs bg-white/20 px-3 py-1 rounded">
                                等待对比...
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-1 overflow-hidden">
                        <!-- 侧边栏导航 -->
                        <div id="diff-sidebar" class="w-16 bg-gray-50 border-r border-gray-200 overflow-y-auto">
                            <div class="p-2 text-xs text-gray-500 font-medium text-center border-b">导航</div>
                            <div id="diff-navigation" class="p-1"></div>
                        </div>
                        <!-- 对比结果内容 -->
                        <div id="diff-result" class="flex-1 bg-gray-50 p-4 overflow-auto font-mono text-sm">
                            <div class="text-center text-gray-400 py-8">
                                <i class="fas fa-code-branch text-4xl mb-4"></i>
                                <p>请在上方输入两段文本，然后点击"开始对比"按钮</p>
                                <p class="text-xs mt-2">支持实时对比、双向分析、智能差异检测</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="text-compare-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
            </div>

            <!-- 文本转换工具 -->
            <div id="text-convert-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-4">文本转换工具</h2>

                <!-- 选项卡导航 -->
                <div class="flex border-b mb-4 flex-shrink-0">
                    <button data-tab="text-convert-tab-1" class="text-convert-tab-btn py-2 px-4 text-sm font-medium text-center active">字符编码检测</button>
                    <button data-tab="text-convert-tab-2" class="text-convert-tab-btn py-2 px-4 text-sm font-medium text-center">HTML实体编码</button>
                    <button data-tab="text-convert-tab-3" class="text-convert-tab-btn py-2 px-4 text-sm font-medium text-center">URL编码解码</button>
                    <button data-tab="text-convert-tab-4" class="text-convert-tab-btn py-2 px-4 text-sm font-medium text-center">Unicode编码</button>
                </div>
                <div id="text-convert-message-area" class="text-center text-sm text-red-600 h-5 mb-2 flex-shrink-0"></div>

                <div class="flex-1 overflow-y-auto relative">
                    <!-- Tab 1: 字符编码检测 -->
                    <div id="text-convert-tab-1" class="text-convert-tab-content">
                        <div class="max-w-4xl mx-auto">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- 输入区域 -->
                                <div class="space-y-4">
                                    <h3 class="text-lg font-medium text-gray-800 flex items-center">
                                        <i class="fas fa-file-text text-blue-500 mr-2"></i>文本输入
                                    </h3>
                                    <div>
                                        <label for="encoding-input" class="block text-sm font-medium text-gray-600 mb-2">输入需要检测编码的文本:</label>
                                        <textarea id="encoding-input" rows="8" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-y font-mono text-sm" placeholder="粘贴或输入文本，自动检测编码格式..."></textarea>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button id="detect-encoding-btn" class="flex-1 bg-blue-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                                            <i class="fas fa-search mr-2"></i>检测编码
                                        </button>
                                        <button id="clear-encoding-btn" class="bg-gray-500 text-white font-bold py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors">
                                            <i class="fas fa-eraser mr-2"></i>清空
                                        </button>
                                    </div>
                                </div>

                                <!-- 结果区域 -->
                                <div class="space-y-4">
                                    <h3 class="text-lg font-medium text-gray-800 flex items-center">
                                        <i class="fas fa-info-circle text-green-500 mr-2"></i>检测结果
                                    </h3>
                                    <div class="bg-gray-50 rounded-lg p-4 border">
                                        <div id="encoding-result" class="space-y-3">
                                            <div class="text-center text-gray-400 py-8">
                                                <i class="fas fa-code text-3xl mb-2"></i>
                                                <p>请输入文本进行编码检测</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 编码转换区域 -->
                                    <div class="space-y-3">
                                        <h4 class="text-md font-medium text-gray-700">编码转换</h4>
                                        <div class="flex space-x-2">
                                            <select id="source-encoding" class="flex-1 p-2 border border-gray-300 rounded-lg text-sm">
                                                <option value="auto">自动检测</option>
                                                <option value="utf-8">UTF-8</option>
                                                <option value="gbk">GBK</option>
                                                <option value="gb2312">GB2312</option>
                                                <option value="big5">Big5</option>
                                                <option value="iso-8859-1">ISO-8859-1</option>
                                            </select>
                                            <i class="fas fa-arrow-right text-gray-400 self-center"></i>
                                            <select id="target-encoding" class="flex-1 p-2 border border-gray-300 rounded-lg text-sm">
                                                <option value="utf-8">UTF-8</option>
                                                <option value="gbk">GBK</option>
                                                <option value="gb2312">GB2312</option>
                                                <option value="big5">Big5</option>
                                                <option value="iso-8859-1">ISO-8859-1</option>
                                            </select>
                                        </div>
                                        <button id="convert-encoding-btn" class="w-full bg-green-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                            <i class="fas fa-exchange-alt mr-2"></i>转换编码
                                        </button>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600 mb-1">转换结果:</label>
                                            <textarea id="encoding-convert-output" rows="4" class="w-full p-3 bg-gray-100 border rounded-lg text-sm font-mono resize-none" readonly placeholder="转换结果将显示在这里..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab 2: HTML实体编码/解码 -->
                    <div id="text-convert-tab-2" class="text-convert-tab-content tab-hidden">
                        <div class="max-w-4xl mx-auto">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- 原始文本 -->
                                <div class="space-y-4">
                                    <h3 class="text-lg font-medium text-gray-800 flex items-center">
                                        <i class="fas fa-code text-orange-500 mr-2"></i>原始文本
                                    </h3>
                                    <div>
                                        <label for="html-entity-input" class="block text-sm font-medium text-gray-600 mb-2">输入原始文本或HTML实体:</label>
                                        <textarea id="html-entity-input" rows="10" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition duration-200 resize-y font-mono text-sm" placeholder="例如: <div>中文</div> 或 &lt;div&gt;&#20013;&#25991;&lt;/div&gt;"></textarea>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button id="html-encode-btn" class="flex-1 bg-orange-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors">
                                            <i class="fas fa-arrow-down mr-2"></i>编码
                                        </button>
                                        <button id="html-decode-btn" class="flex-1 bg-blue-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                                            <i class="fas fa-arrow-up mr-2"></i>解码
                                        </button>
                                        <button id="clear-html-entity-btn" class="bg-gray-500 text-white font-bold py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors">
                                            <i class="fas fa-eraser mr-2"></i>清空
                                        </button>
                                    </div>
                                </div>

                                <!-- 转换结果 -->
                                <div class="space-y-4">
                                    <h3 class="text-lg font-medium text-gray-800 flex items-center">
                                        <i class="fas fa-magic text-purple-500 mr-2"></i>转换结果
                                    </h3>
                                    <div>
                                        <label for="html-entity-output" class="block text-sm font-medium text-gray-600 mb-2">转换结果:</label>
                                        <textarea id="html-entity-output" rows="10" class="w-full p-3 bg-gray-100 border rounded-lg text-sm font-mono resize-y" readonly placeholder="转换结果将显示在这里..."></textarea>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button id="copy-html-entity-btn" class="flex-1 bg-green-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                            <i class="fas fa-copy mr-2"></i>复制结果
                                        </button>
                                        <button id="load-html-example-btn" class="bg-indigo-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors">
                                            <i class="fas fa-download mr-2"></i>示例
                                        </button>
                                    </div>

                                    <!-- 常用HTML实体参考 -->
                                    <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                                        <h4 class="text-sm font-medium text-blue-800 mb-2">常用HTML实体参考</h4>
                                        <div class="grid grid-cols-2 gap-2 text-xs text-blue-700">
                                            <div>&lt; → &amp;lt;</div>
                                            <div>&gt; → &amp;gt;</div>
                                            <div>&amp; → &amp;amp;</div>
                                            <div>&quot; → &amp;quot;</div>
                                            <div>&apos; → &amp;apos;</div>
                                            <div>空格 → &amp;nbsp;</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab 3: URL编码/解码 -->
                    <div id="text-convert-tab-3" class="text-convert-tab-content tab-hidden">
                        <div class="max-w-4xl mx-auto">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- 原始文本 -->
                                <div class="space-y-4">
                                    <h3 class="text-lg font-medium text-gray-800 flex items-center">
                                        <i class="fas fa-link text-green-500 mr-2"></i>原始文本
                                    </h3>
                                    <div>
                                        <label for="url-encode-input" class="block text-sm font-medium text-gray-600 mb-2">输入原始文本或URL编码:</label>
                                        <textarea id="url-encode-input" rows="10" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-200 resize-y font-mono text-sm" placeholder="例如: 中文测试 或 %E4%B8%AD%E6%96%87%E6%B5%8B%E8%AF%95"></textarea>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button id="url-encode-btn" class="flex-1 bg-green-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                            <i class="fas fa-arrow-down mr-2"></i>URL编码
                                        </button>
                                        <button id="url-decode-btn" class="flex-1 bg-blue-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                                            <i class="fas fa-arrow-up mr-2"></i>URL解码
                                        </button>
                                        <button id="clear-url-encode-btn" class="bg-gray-500 text-white font-bold py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors">
                                            <i class="fas fa-eraser mr-2"></i>清空
                                        </button>
                                    </div>
                                </div>

                                <!-- 转换结果 -->
                                <div class="space-y-4">
                                    <h3 class="text-lg font-medium text-gray-800 flex items-center">
                                        <i class="fas fa-globe text-indigo-500 mr-2"></i>转换结果
                                    </h3>
                                    <div>
                                        <label for="url-encode-output" class="block text-sm font-medium text-gray-600 mb-2">转换结果:</label>
                                        <textarea id="url-encode-output" rows="10" class="w-full p-3 bg-gray-100 border rounded-lg text-sm font-mono resize-y" readonly placeholder="转换结果将显示在这里..."></textarea>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button id="copy-url-encode-btn" class="flex-1 bg-green-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                            <i class="fas fa-copy mr-2"></i>复制结果
                                        </button>
                                        <button id="load-url-example-btn" class="bg-indigo-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors">
                                            <i class="fas fa-download mr-2"></i>示例
                                        </button>
                                    </div>

                                    <!-- URL编码说明 -->
                                    <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                                        <h4 class="text-sm font-medium text-green-800 mb-2">URL编码说明</h4>
                                        <div class="text-xs text-green-700 space-y-1">
                                            <div>• 中文字符会被编码为 %XX%XX%XX 格式</div>
                                            <div>• 空格编码为 %20 或 +</div>
                                            <div>• 特殊字符如 &amp;、=、? 等会被编码</div>
                                            <div>• 爬虫开发中最常见的编码问题</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab 4: Unicode编码/解码 -->
                    <div id="text-convert-tab-4" class="text-convert-tab-content tab-hidden">
                        <div class="max-w-4xl mx-auto">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- 原始文本 -->
                                <div class="space-y-4">
                                    <h3 class="text-lg font-medium text-gray-800 flex items-center">
                                        <i class="fas fa-language text-purple-500 mr-2"></i>原始文本
                                    </h3>
                                    <div>
                                        <label for="unicode-input" class="block text-sm font-medium text-gray-600 mb-2">输入原始文本或Unicode编码:</label>
                                        <textarea id="unicode-input" rows="10" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition duration-200 resize-y font-mono text-sm" placeholder="例如: 中文 或 \u4e2d\u6587"></textarea>
                                    </div>
                                    <div class="space-y-2">
                                        <div class="flex space-x-2">
                                            <button id="unicode-encode-btn" class="flex-1 bg-purple-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors">
                                                <i class="fas fa-arrow-down mr-2"></i>Unicode编码
                                            </button>
                                            <button id="unicode-decode-btn" class="flex-1 bg-blue-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                                                <i class="fas fa-arrow-up mr-2"></i>Unicode解码
                                            </button>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button id="clear-unicode-btn" class="flex-1 bg-gray-500 text-white font-bold py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors">
                                                <i class="fas fa-eraser mr-2"></i>清空
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Unicode格式选项 -->
                                    <div class="bg-purple-50 rounded-lg p-3 border border-purple-200">
                                        <h4 class="text-sm font-medium text-purple-800 mb-2">编码格式选项</h4>
                                        <div class="space-y-2">
                                            <label class="flex items-center text-sm text-purple-700">
                                                <input type="radio" name="unicode-format" value="\\u" class="mr-2" checked>
                                                \\uXXXX 格式 (如: \\u4e2d)
                                            </label>
                                            <label class="flex items-center text-sm text-purple-700">
                                                <input type="radio" name="unicode-format" value="\\U" class="mr-2">
                                                \\UXXXXXXXX 格式 (如: \\U00004e2d)
                                            </label>
                                            <label class="flex items-center text-sm text-purple-700">
                                                <input type="radio" name="unicode-format" value="&#" class="mr-2">
                                                &amp;#数字; 格式 (如: &amp;#20013;)
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- 转换结果 -->
                                <div class="space-y-4">
                                    <h3 class="text-lg font-medium text-gray-800 flex items-center">
                                        <i class="fas fa-code text-pink-500 mr-2"></i>转换结果
                                    </h3>
                                    <div>
                                        <label for="unicode-output" class="block text-sm font-medium text-gray-600 mb-2">转换结果:</label>
                                        <textarea id="unicode-output" rows="10" class="w-full p-3 bg-gray-100 border rounded-lg text-sm font-mono resize-y" readonly placeholder="转换结果将显示在这里..."></textarea>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button id="copy-unicode-btn" class="flex-1 bg-green-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                            <i class="fas fa-copy mr-2"></i>复制结果
                                        </button>
                                        <button id="load-unicode-example-btn" class="bg-indigo-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors">
                                            <i class="fas fa-download mr-2"></i>示例
                                        </button>
                                    </div>

                                    <!-- Unicode字符信息 -->
                                    <div id="unicode-char-info" class="bg-pink-50 rounded-lg p-4 border border-pink-200">
                                        <h4 class="text-sm font-medium text-pink-800 mb-2">字符信息</h4>
                                        <div id="unicode-char-details" class="text-xs text-pink-700">
                                            <div class="text-center text-gray-400 py-4">
                                                输入文本后显示字符详细信息
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </main>
    </div>

    <div id="copy-modal" class="hidden fixed top-5 right-5 bg-green-500 text-white py-2 px-4 rounded-lg shadow-lg opacity-0">复制成功!</div>

    <!-- 帮助模态框 -->
    <div id="help-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-xl shadow-2xl max-w-4xl max-h-[90vh] overflow-y-auto m-4">
            <div class="sticky top-0 bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-6 rounded-t-xl">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-question-circle text-2xl"></i>
                        <div>
                            <h3 class="text-xl font-bold">文本对比工具使用指南</h3>
                            <p class="text-blue-100 text-sm">功能介绍与快捷键说明</p>
                        </div>
                    </div>
                    <button id="close-help-modal" class="text-white hover:text-blue-200 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- 功能介绍 -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-star text-yellow-500 mr-2"></i>主要功能
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h5 class="font-medium text-blue-800 mb-2">智能对比</h5>
                            <p class="text-sm text-blue-600">支持逐行、逐字符对比，智能识别文本差异</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h5 class="font-medium text-green-800 mb-2">双向分析</h5>
                            <p class="text-sm text-green-600">A→B 和 B→A 双向对比，清晰展示变化方向</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h5 class="font-medium text-purple-800 mb-2">实时对比</h5>
                            <p class="text-sm text-purple-600">输入时自动对比，即时查看差异结果</p>
                        </div>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h5 class="font-medium text-orange-800 mb-2">主题切换</h5>
                            <p class="text-sm text-orange-600">支持明暗主题，适应不同使用环境</p>
                        </div>
                    </div>
                </div>

                <!-- 快捷键 -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-keyboard text-indigo-500 mr-2"></i>快捷键
                    </h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">开始对比</span>
                                <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl + Enter</kbd>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">清空全部</span>
                                <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl + D</kbd>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">撤销</span>
                                <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl + Z</kbd>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">重做</span>
                                <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl + Y</kbd>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">搜索</span>
                                <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl + F</kbd>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">全屏模式</span>
                                <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">F11</kbd>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 使用技巧 -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>使用技巧
                    </h4>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-blue-600 text-xs font-bold">1</span>
                            </div>
                            <div>
                                <p class="text-sm text-gray-700"><strong>拖拽调整：</strong>可以拖拽中间的分隔线调整左右面板大小</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-green-600 text-xs font-bold">2</span>
                            </div>
                            <div>
                                <p class="text-sm text-gray-700"><strong>侧边导航：</strong>右侧导航栏显示所有差异位置，点击快速跳转</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-purple-600 text-xs font-bold">3</span>
                            </div>
                            <div>
                                <p class="text-sm text-gray-700"><strong>搜索功能：</strong>在对比结果中搜索特定内容，支持正则表达式</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-orange-600 text-xs font-bold">4</span>
                            </div>
                            <div>
                                <p class="text-sm text-gray-700"><strong>对比选项：</strong>根据需要选择忽略空白字符、大小写等选项</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 示例数据 -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-code text-green-500 mr-2"></i>示例数据
                    </h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-sm text-gray-600 mb-3">点击"载入示例"按钮可以快速体验对比功能：</p>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                            <div>
                                <h6 class="font-medium text-red-700 mb-2">文本 A (原始版本)</h6>
                                <pre class="bg-red-50 p-2 rounded text-red-600 overflow-x-auto">function hello() {
    console.log("Hello World");
    return true;
}</pre>
                            </div>
                            <div>
                                <h6 class="font-medium text-green-700 mb-2">文本 B (修改版本)</h6>
                                <pre class="bg-green-50 p-2 rounded text-green-600 overflow-x-auto">function hello(name) {
    console.log("Hello " + name);
    console.log("Welcome!");
    return true;
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 外部自定义脚本 -->
    <script src="/static/js/main.js" defer></script>
</body>
</html>
