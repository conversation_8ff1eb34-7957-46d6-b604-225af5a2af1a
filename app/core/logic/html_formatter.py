# 文件路径: app/core/logic/html_formatter.py

from bs4 import BeautifulSoup
from bs4.formatter import HTMLFormatter
import re
import uuid


def _escape(text: str) -> str:
    """对文本进行HTML转义。"""
    if not text: return ''
    return text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;').replace('"', '&quot;').replace("'",
                                                                                                               '&#39;')


def _highlight_html_code(escaped_html_string: str) -> str:
    """
    【最终完美版】为已经HTML转义过的HTML代码字符串添加语法高亮。
    采用“隔离替换”策略，彻底杜绝正则表达式的连锁反应问题。
    """
    placeholders = {}

    # 步骤 1: 隔离注释和DOCTYPE，用唯一的占位符替换它们
    def isolate(match):
        placeholder = f"__PLACEHOLDER_{uuid.uuid4()}__"
        # 将高亮好的、完整的<span>标签存起来
        placeholders[placeholder] = f'<span class="text-gray-500">{match.group(0)}</span>'
        return placeholder

    # 优先处理注释和DOCTYPE
    temp_str = re.sub(r'&lt;!--.*?--&gt;', isolate, escaped_html_string, flags=re.DOTALL)
    temp_str = re.sub(r'&lt;!DOCTYPE.*&gt;', isolate, temp_str, flags=re.IGNORECASE)

    # 步骤 2: 对剩余的纯净HTML进行连锁高亮，现在是绝对安全的
    highlighted = temp_str
    highlighted = re.sub(r'(=)(".*?"|\'.*?\')', r'\1<span class="text-red-700">\2</span>', highlighted)
    highlighted = re.sub(r'(\s+)([\w\-\:]+)(?==)', r'\1<span class="text-green-700">\2</span>', highlighted)
    highlighted = re.sub(r'(&lt;/?)([\w\-\:]+)', r'\1<span class="text-purple-600">\2</span>', highlighted)

    # 步骤 3: 将占位符替换回它们对应的高亮好的内容
    for placeholder, replacement in placeholders.items():
        highlighted = highlighted.replace(placeholder, replacement)

    return highlighted


class CustomHTMLFormatter(HTMLFormatter):
    """
    自定义HTML格式化器，用于控制缩进。
    这里我们将默认的单空格缩进改为了标准的4个空格。
    """

    def __init__(self):
        super().__init__(indent="    ")  # 使用4个空格作为缩进


def format_html_code(raw_html_str: str) -> tuple[str, str, str | None]:
    """
    【最终完美版】将原始HTML字符串格式化为美观的、带高亮的代码。
    采用 BeautifulSoup + 自定义格式化器 + 隔离高亮策略，确保结果的稳定与美观。
    """
    if not raw_html_str.strip():
        return "", "", "输入内容为空。"
    try:
        # 使用 BeautifulSoup 和 lxml 解析器
        soup = BeautifulSoup(raw_html_str, 'lxml')

        # 使用我们自定义的格式化器来调用 .prettify()
        formatter = CustomHTMLFormatter()
        plain_output = soup.prettify(formatter=formatter)

        # 进行HTML转义和语法高亮
        escaped_plain = _escape(plain_output)
        html_output = _highlight_html_code(escaped_plain)

        return html_output, plain_output.strip(), None
    except Exception as e:
        return "", "", f"HTML解析或格式化时发生错误: {e}"