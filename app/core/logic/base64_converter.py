# 文件路径: app/core/logic/base64_converter.py

import base64
import sys


def encode_to_base64(plain_text: str, encoding: str = 'utf-8') -> tuple[str | None, str | None]:
    """将普通文本编码为Base64字符串。"""
    try:
        text_bytes = plain_text.encode(encoding)
        base64_bytes = base64.b64encode(text_bytes)
        base64_string = base64_bytes.decode('ascii')
        return base64_string, None
    except Exception as e:
        return None, f"编码失败: {e}"


def decode_from_base64(b64_string: str, encoding: str = 'utf-8') -> tuple[str | None, str | None]:
    """将Base64字符串解码为普通文本。"""
    try:
        base64_bytes = b64_string.strip().encode('ascii')
        padding_needed = len(base64_bytes) % 4
        if padding_needed:
            base64_bytes += b'=' * (4 - padding_needed)

        decoded_bytes = base64.b64decode(base64_bytes)
        plain_text = decoded_bytes.decode(encoding)
        return plain_text, None
    except (base64.binascii.Error, UnicodeDecodeError) as e:
        return None, f"解码失败: 输入的可能不是有效的Base64字符串或编码格式不匹配。({e})"
    except Exception as e:
        return None, f"解码失败: 发生未知错误。({e})"


# --- VVV 请在文件末尾添加以下调试代码 VVV ---
# 当这个文件被Python加载时，下面的信息会立即打印在Pycharm终端
print("--- [DEBUG FINGERPRINT] logic/base64_converter.py 模块被成功加载 ---", file=sys.stderr)
print(f"--- [DEBUG FINGERPRINT] 当前模块中可用的名称: {dir()}", file=sys.stderr)