# -*- coding: utf-8 -*-
# 文件路径: app/core/logic/url_parser.py

from urllib.parse import urlparse, parse_qs, unquote


def format_dict_to_python_code(data_dict: dict, dict_name: str) -> tuple[str, str]:
    """将字典格式化为带HTML高亮的Python代码字符串。"""
    if not data_dict:
        empty_str = f"{dict_name} = {{}}"
        return empty_str, empty_str

    html_lines = [f'<span class="text-gray-500">{dict_name}</span> = {{']
    plain_lines = [f'{dict_name} = {{']

    for key, value in data_dict.items():
        # parse_qs返回的值是列表，我们通常取第一个
        # 如果列表为空，则值为空字符串
        val = value[0] if value else ""
        escaped_value = val.replace('"', '\\"')

        html_lines.append(
            f'    <span class="text-green-700">"{key}"</span>: <span class="text-red-700">"{escaped_value}"</span>,')
        plain_lines.append(f'    "{key}": "{escaped_value}",')

    html_lines.append('}')
    plain_lines.append('}')

    html_output = "\n".join(html_lines)
    plain_output = "\n".join(plain_lines)
    return html_output, plain_output


def parse_url_and_extract_params(raw_url: str) -> tuple[str, str, str, str | None]:
    """
    解析URL，提取基础URL和查询参数。
    """
    if not raw_url.strip():
        return "", "", "", "URL输入为空。"
    try:
        # 1. 解析URL
        parsed_url = urlparse(raw_url)

        # 2. 提取并格式化基础URL
        base_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
        base_url_html = f'<span class="text-gray-500">url</span> = <span class="text-red-700">"{base_url}"</span>'
        base_url_plain = f'url = "{base_url}"'

        # 3. 提取并解码查询参数
        # parse_qs 会自动处理URL解码，并将结果存为字典，值为列表
        params_dict = parse_qs(parsed_url.query)

        # 4. 将参数字典格式化为带高亮的Python代码
        params_html, params_plain = format_dict_to_python_code(params_dict, "params")

        return base_url_html, base_url_plain, params_html, params_plain, None

    except Exception as e:
        return "", "", "", "", f"URL解析失败: {e}"