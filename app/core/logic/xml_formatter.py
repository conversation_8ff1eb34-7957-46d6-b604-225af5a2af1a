# 文件路径: app/core/logic/xml_formatter.py

from lxml import etree
import re


def _escape(text: str) -> str:
    """对文本进行HTML转义。"""
    if not text:
        return ''
    return (
        text.replace('&', '&amp;')
        .replace('<', '&lt;')
        .replace('>', '&gt;')
        .replace('"', '&quot;')
        .replace("'", '&#39;')
    )


def _highlight_xml(escaped_xml_string: str) -> str:
    """【最终版】为已经HTML转义过的XML字符串添加语法高亮。"""
    pattern = re.compile(
        r"(?P<comment>&lt;!--.*?--&gt;)|"
        r"(?P<declaration>&lt;\?xml.*?\?&gt;)|"
        r"(?P<tag_open>&lt;/?)(?P<tag_name>[\w_:\-]+)|"
        r"(?P<tag_close>&gt;)|"
        r"(?P<attr_name>\s[\w_:\-]+)(?P<equals_sign>=)(?P<attr_value>&quot;.*?&quot;|'.*?')",
        re.DOTALL
    )

    def replacer(match):
        """根据匹配到的命名组，返回带<span>包裹的替换字符串"""
        if match.group('comment'):
            return f'<span class="text-gray-500">{match.group("comment")}</span>'
        if match.group('declaration'):
            return f'<span class="text-gray-500">{match.group("declaration")}</span>'
        if match.group('tag_name'):
            return f'{match.group("tag_open")}<span class="text-purple-600">{match.group("tag_name")}</span>'
        if match.group('tag_close'):
            return match.group('tag_close')
        if match.group('attr_name'):
            attr_name_html = f'<span class="text-green-700">{match.group("attr_name")}</span>'
            attr_value_html = f'<span class="text-red-700">{match.group("attr_value")}</span>'
            return f'{attr_name_html}{match.group("equals_sign")}{attr_value_html}'
        return match.group(0)

    return pattern.sub(replacer, escaped_xml_string)


def format_xml_to_code(raw_xml_str: str, compress: bool = False) -> tuple[str, str, str]:
    """
    【最终版】将原始XML字符串格式化。
    能智能处理标准XML文档和多根节点的XML片段。
    """
    if not raw_xml_str.strip():
        return "", "", "输入内容为空。"

    plain_output = ""

    try:
        # --- VVV 这是修复的关键 VVV ---
        # 步骤 1: 使用严格的解析器（没有 recover=True），尝试作为标准XML文档解析
        strict_parser = etree.XMLParser(remove_blank_text=True, strip_cdata=False)
        cleaned_str = re.sub(r'^\s*<\?xml[^>]*\?>', '', raw_xml_str, count=1).strip()
        root = etree.fromstring(cleaned_str.encode('utf-8'), strict_parser)
        plain_output = etree.tostring(root, pretty_print=True, xml_declaration=True, encoding='UTF-8').decode('utf-8')

    except etree.XMLSyntaxError:
        # 步骤 2: 如果严格解析失败（说明是片段），则回退到专门处理片段的逻辑
        try:
            # 在这里我们使用宽容的解析器（有 recover=True）来处理可能的轻微错误
            lenient_parser = etree.XMLParser(remove_blank_text=True, strip_cdata=False, recover=True)
            cleaned_str = re.sub(r'^\s*<\?xml[^>]*\?>', '', raw_xml_str, count=1).strip()
            wrapped_xml = f"<root>{cleaned_str}</root>".encode('utf-8')
            root = etree.fromstring(wrapped_xml, parser=lenient_parser)

            formatted_parts = []
            for element in root:
                part = etree.tostring(element, pretty_print=True, encoding='unicode')
                formatted_parts.append(part)
            plain_output = "\n".join(formatted_parts)

        except Exception as e:
            return "", "", f"XML语法错误或格式无效: {e}"

    if compress:
        # 压缩逻辑现在基于已经格式化好的 plain_output，更加可靠
        try:
            # 重新解析一次是为了消除所有空白
            parser = etree.XMLParser(remove_blank_text=True)
            # 为了正确压缩片段，我们也需要包裹它
            wrapped_for_compress = f"<root>{plain_output}</root>".encode('utf-8')
            root = etree.fromstring(wrapped_for_compress, parser)
            # 压缩时只输出子元素的内容
            plain_output = "".join([etree.tostring(child, encoding='unicode') for child in root])
        except etree.XMLSyntaxError:
            # 如果出现解析错误，则退回到简单的字符串处理
            plain_output = ''.join(line.strip() for line in plain_output.splitlines())

    # 进行HTML转义和语法高亮
    escaped_plain = _escape(plain_output)
    html_output = _highlight_xml(escaped_plain)

    return html_output, plain_output.strip(), None

