# -*- coding: utf-8 -*-
"""
核心逻辑函数，用于解析浏览器请求头。
这些函数是纯粹的，不依赖于任何Web框架。
"""


def parse_raw_headers_to_dict(raw_headers: str) -> dict:
    """
    将原始浏览器请求头字符串转换为字典。
    可以处理从Chrome开发者工具复制的、键和值在不同行的格式，
    也能处理标准的 'key: value' 格式。

    Args:
        raw_headers (str): 从浏览器复制的原始请求头字符串。

    Returns:
        dict: 转换后的headers字典。
    """
    headers = {}
    lines = raw_headers.strip().split('\n')
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        # 跳过空行和 HTTP/2 伪标头 (以':'开头)
        if not line or line.startswith(':'):
            i += 1
            continue

        # 标准的 'key: value' 格式
        if ':' in line:
            key, value = line.split(':', 1)
            key = key.strip()
            value = value.strip()
            # 确保键不为空
            if key:
                headers[key] = value
        # Chrome的 'key' 在一行, 'value' 在下一行的格式
        elif i + 1 < len(lines):
            key = line
            # 下一行就是值，我们直接取用，不做过多判断
            value = lines[i + 1].strip()
            headers[key] = value
            i += 1  # 跳过下一行，因为它已经是值了

        i += 1
    return headers


def parse_cookies_from_string(cookie_string: str) -> dict:
    """
    将cookie字符串解析为字典。

    Args:
        cookie_string (str): 从 'cookie' 请求头中获取的cookie字符串。

    Returns:
        dict: 解析后的cookies字典。
    """
    cookies = {}
    if not cookie_string:
        return cookies

    for item in cookie_string.split(';'):
        item = item.strip()
        if '=' in item:
            key, value = item.split('=', 1)
            cookies[key.strip()] = value.strip()
    return cookies


def format_dict_to_python_code(data_dict: dict, dict_name: str) -> tuple[str, str]:
    """
    将字典格式化为带HTML颜色和纯文本的Python代码字符串。

    Args:
        data_dict (dict): 需要格式化的字典。
        dict_name (str): 在代码中为字典指定的变量名。

    Returns:
        tuple[str, str]: 一个元组，包含 (用于显示的HTML字符串, 用于复制的纯文本字符串)。
    """
    if not data_dict:
        empty_str = f"{dict_name} = {{}}"
        return empty_str, empty_str

    html_lines = [f'<span class="text-gray-500">{dict_name}</span> = {{']
    plain_lines = [f'{dict_name} = {{']

    for key, value in data_dict.items():
        # 为了在Python代码中正确显示，转义值中的双引号
        escaped_value = value.replace('"', '\\"')
        html_lines.append(
            f'    <span class="text-green-700">"{key}"</span>: <span class="text-red-700">"{escaped_value}"</span>,')
        plain_lines.append(f'    "{key}": "{escaped_value}",')

    html_lines.append('}')
    plain_lines.append('}')

    html_output = "\n".join(html_lines)
    plain_output = "\n".join(plain_lines)

    return html_output, plain_output

