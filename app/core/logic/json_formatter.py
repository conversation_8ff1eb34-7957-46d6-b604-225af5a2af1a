# -*- coding: utf-8 -*-
"""
核心逻辑函数，用于格式化JSON或Python字典。
"""
import json
import ast


def _parse_string_to_obj(input_str: str) -> object:
    """
    智能解析输入字符串。
    可以处理标准JSON、Python字典字面量，以及被转义成字符串的JSON。
    """
    parsed_obj = None
    if not input_str.strip():
        raise ValueError("输入内容为空。")

    try:
        # 优先尝试标准JSON解析
        parsed_obj = json.loads(input_str)
    except json.JSONDecodeError:
        try:
            # 如果JSON解析失败，尝试将其作为Python的字典或列表字面量来解析
            parsed_obj = ast.literal_eval(input_str)
        except Exception:
            # 如果两种方式都失败，则抛出异常
            raise ValueError("输入内容既不是有效的JSON，也不是有效的Python字典/列表。")

    # 循环检查：如果解析结果仍然是一个字符串，说明它可能是被转义过的JSON，需要继续解析
    # 这个循环可以处理多层转义的情况
    while isinstance(parsed_obj, str):
        try:
            # 尝试对解析出来的字符串再次进行JSON解析
            parsed_obj = json.loads(parsed_obj)
        except (json.JSONDecodeError, TypeError):
            # 如果无法继续解析，说明它就是一个普通的字符串，跳出循环
            break

    return parsed_obj


def _format_recursive_html(data: object, level: int = 0) -> str:
    """
    递归函数，用于生成支持折叠的、带颜色高亮的HTML格式的JSON/字典字符串。
    采用紧凑布局，类似于现代JSON查看器的风格。
    """
    indent = "  " * level  # 使用2个空格缩进

    if isinstance(data, dict):
        if not data:
            return '<span class="json-bracket">{}</span>'

        # 计算项目数量用于显示
        item_count = len(data)

        # 创建可折叠的对象
        toggle_id = f"obj_{id(data)}_{level}"

        # 对象开始标签，包含折叠功能和项目计数
        result = f'<span class="json-toggle" data-target="{toggle_id}">'
        result += f'<i class="fas fa-minus-square json-toggle-icon"></i></span>'
        result += f'<span class="json-bracket">{{</span>'
        result += f'<span class="json-count text-gray-400 text-xs ml-1">{item_count} items</span>'

        # 可折叠的内容区域
        result += f'<div class="json-content" id="{toggle_id}">'

        items = []
        for i, (key, value) in enumerate(data.items()):
            is_last = (i == len(data) - 1)
            comma = "" if is_last else ","

            # 键值对行
            key_html = f'<span class="json-key">"{key}"</span>'
            value_html = _format_recursive_html(value, level + 1)

            # 如果值是复杂类型，换行显示；如果是简单类型，同行显示
            if isinstance(value, (dict, list)) and value:
                item_line = f'<div class="json-line">{indent}  {key_html}: {value_html}{comma}</div>'
            else:
                item_line = f'<div class="json-line">{indent}  {key_html}: {value_html}{comma}</div>'

            items.append(item_line)

        result += ''.join(items)
        result += f'<div class="json-line">{indent}<span class="json-bracket">}}</span></div>'
        result += '</div>'

        return result

    elif isinstance(data, list):
        if not data:
            return '<span class="json-bracket">[]</span>'

        # 计算项目数量用于显示
        item_count = len(data)

        # 创建可折叠的数组
        toggle_id = f"arr_{id(data)}_{level}"

        # 数组开始标签，包含折叠功能和项目计数
        result = f'<span class="json-toggle" data-target="{toggle_id}">'
        result += f'<i class="fas fa-minus-square json-toggle-icon"></i></span>'
        result += f'<span class="json-bracket">[</span>'
        result += f'<span class="json-count text-gray-400 text-xs ml-1">{item_count} items</span>'

        # 可折叠的内容区域
        result += f'<div class="json-content" id="{toggle_id}">'

        items = []
        for i, item in enumerate(data):
            is_last = (i == len(data) - 1)
            comma = "" if is_last else ","

            item_html = _format_recursive_html(item, level + 1)
            item_line = f'<div class="json-line">{indent}  {item_html}{comma}</div>'
            items.append(item_line)

        result += ''.join(items)
        result += f'<div class="json-line">{indent}<span class="json-bracket">]</span></div>'
        result += '</div>'

        return result

    # 原始类型的渲染 - 紧凑显示
    elif isinstance(data, str):
        escaped_str = data.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;').replace('"', '&quot;')
        return f'<span class="json-string">"{escaped_str}"</span>'
    elif isinstance(data, (int, float)):
        return f'<span class="json-number">{data}</span>'
    elif isinstance(data, bool):
        return f'<span class="json-boolean">{str(data).lower()}</span>'
    elif data is None:
        return f'<span class="json-null">null</span>'
    else:
        return str(data)


def format_json_to_code(raw_str: str, sort_keys: bool = False, compress: bool = False) -> tuple[str, str]:
    """
    将原始JSON或Python字典字符串格式化为带HTML颜色和纯文本的字符串。

    Args:
        raw_str (str): 原始JSON或Python字典字符串。
        sort_keys (bool): 是否按键排序。
        compress (bool): 是否压缩JSON。

    Returns:
        tuple[str, str]: 一个元组，包含 (用于显示的HTML字符串, 用于复制的纯文本字符串)。
    """
    obj = _parse_string_to_obj(raw_str)

    indent = None if compress else 4

    # 生成用于复制的纯文本
    plain_output = json.dumps(obj, indent=indent, sort_keys=sort_keys, ensure_ascii=False)

    # 如果是压缩模式，HTML输出就是纯文本
    if compress:
        return plain_output, plain_output

    # 如果需要排序，需要先在Python对象层面处理
    if sort_keys:
        obj = json.loads(plain_output)

    html_output = _format_recursive_html(obj)

    return html_output, plain_output
