document.addEventListener('DOMContentLoaded', () => {
    // --- 示例数据 ---
    const MOCK_HEADERS_DATA = `Host: www.example.com
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8
Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2
Connection: keep-alive
Cookie: session_id=abc123xyz456; theme=dark`;

    const MOCK_JSON_DATA = `{
    "user_id": 12345, "username": "爬虫爱好者", "is_active": true,
    "roles": ["admin", "editor"],
    "profile": {"email": "<EMAIL>", "address": null, "preferences": {"theme": "dark", "notifications": {"email": true, "sms": false}}},
    "logs": [{"timestamp": 1672531200, "action": "login"}, {"timestamp": 1672534800, "action": "update_profile"}]
}`;

    const MOCK_SORTABLE_JSON_DATA = `{
        "zebra": 1, "apple": 2, "metadata": { "version": "1.0", "author": "tester", "timestamp": "2025-01-01" },
        "data": [ {"id": 10, "name": "Xylophone"}, {"id": 2, "name": "Tambourine"} ], "cat": 3
    }`;

    const MOCK_XML_DATA = `<?xml version="1.0" encoding="UTF-8"?><bookstore><book category="cooking"><title lang="en">Everyday Italian</title><author>Giada De Laurentiis</author><year>2005</year><price>30.00</price></book><book category="children"><title lang="en">Harry Potter</title><author>J K. Rowling</author><year>2005</year><price>29.99</price></book></bookstore>`;


    // --- 元素获取 ---
    const sidebarNav = document.getElementById('sidebar-nav');
    const toolContents = document.querySelectorAll('.tool-content');
    const copyModal = document.getElementById('copy-modal');

    // --- 页面路由/工具切换逻辑 ---
    sidebarNav.addEventListener('click', (e) => {
        e.preventDefault();
        const targetLink = e.target.closest('.sidebar-link');
        if (!targetLink) return;
        sidebarNav.querySelectorAll('.sidebar-link').forEach(link => link.classList.remove('active'));
        targetLink.classList.add('active');
        const toolId = targetLink.dataset.tool;
        toolContents.forEach(content => {
            content.id === toolId ? content.classList.remove('hidden') : content.classList.add('hidden');
        });
    });

    // --- Headers 工具逻辑 ---
    const convertHeadersBtn = document.getElementById('convert-headers-btn');
    const rawHeadersInput = document.getElementById('raw-headers');
    const headersOutput = document.getElementById('headers-output');
    const cookiesOutput = document.getElementById('cookies-output');
    const headersMessageArea = document.getElementById('headers-message-area');
    const loadExampleHeadersBtn = document.getElementById('load-example-headers');

    loadExampleHeadersBtn.addEventListener('click', () => {
        rawHeadersInput.value = MOCK_HEADERS_DATA;
        convertHeadersBtn.click();
    });

    convertHeadersBtn.addEventListener('click', async () => {
        headersMessageArea.textContent = '';
        convertHeadersBtn.disabled = true;
        convertHeadersBtn.textContent = '转换中...';
        try {
            const response = await fetch('/api/v1/convert-headers', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ raw_headers: rawHeadersInput.value })
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);
            headersOutput.innerHTML = result.headers_html;
            cookiesOutput.innerHTML = result.cookies_html;
            headersOutput.dataset.plainText = result.headers_plain;
            cookiesOutput.dataset.plainText = result.cookies_plain;
        } catch (error) {
            headersMessageArea.textContent = `错误: ${error.message}`;
        } finally {
            convertHeadersBtn.disabled = false;
            convertHeadersBtn.textContent = '转换格式';
        }
    });

    // 【已修复】使用事件委托为Headers工具添加复制功能
    const headersToolContainer = document.getElementById('headers-tool');
    if (headersToolContainer) {
        headersToolContainer.addEventListener('click', (e) => {
            const copyButton = e.target.closest('.copy-btn');
            if (copyButton) {
                const targetId = copyButton.dataset.copyTarget;
                if (targetId) {
                    window.copyToClipboard(targetId);
                }
            }
        });
    }


    // --- JSON 工具逻辑 ---
    const formatJsonBtn = document.getElementById('format-json-btn');
    const rawJsonInput = document.getElementById('raw-json');
    const jsonOutput = document.getElementById('json-output');
    const jsonMessageArea = document.getElementById('json-message-area');
    const loadExampleJsonBtn = document.getElementById('load-example-json');
    const loadSortableJsonBtn = document.getElementById('load-sortable-json');
    const jsonToolbar = document.getElementById('json-toolbar');
    const jsonSearchBar = document.getElementById('json-search-bar');
    const jsonSearchInput = document.getElementById('json-search-input');
    const jsonSearchClear = document.getElementById('json-search-clear');

    const jsonOptions = {
        sort_keys: false,
        compress: false,
        isEscaped: false,
        isCollapsed: false,
    };

    let originalJsonHTML = '';

    const handleFormatJson = async () => {
        if (!rawJsonInput.value.trim()) {
            jsonOutput.innerHTML = '';
            jsonOutput.dataset.plainText = '';
            return;
        }
        jsonMessageArea.textContent = '';
        formatJsonBtn.disabled = true;
        formatJsonBtn.textContent = '处理中...';
        try {
            const response = await fetch('/api/v1/format-json', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    raw_json: rawJsonInput.value,
                    sort_keys: jsonOptions.sort_keys,
                    compress: jsonOptions.compress
                })
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);

            jsonOutput.innerHTML = result.json_html;
            jsonOutput.dataset.plainText = result.json_plain;
            originalJsonHTML = result.json_html;
        } catch (error) {
            jsonMessageArea.textContent = `错误: ${error.message}`;
            jsonOutput.innerHTML = '';
        } finally {
            formatJsonBtn.disabled = false;
            formatJsonBtn.textContent = '格式化';
        }
    };

    formatJsonBtn.addEventListener('click', handleFormatJson);
    loadExampleJsonBtn.addEventListener('click', () => {
        rawJsonInput.value = MOCK_JSON_DATA;
        handleFormatJson();
    });
    loadSortableJsonBtn.addEventListener('click', () => {
        rawJsonInput.value = MOCK_SORTABLE_JSON_DATA;
        if (!jsonOptions.sort_keys) {
            jsonToolbar.querySelector('[data-action="sort"]').click();
        } else {
            handleFormatJson();
        }
    });

    jsonToolbar.addEventListener('click', (e) => {
        const button = e.target.closest('.tool-btn');
        if (!button) return;
        const action = button.dataset.action;

        switch (action) {
            case 'sort':
            case 'compress':
                jsonOptions[action] = !jsonOptions[action];
                button.classList.toggle('active');
                handleFormatJson();
                break;
            case 'escape':
                handleJsonEscape(button);
                break;
            case 'collapse':
                toggleAllJsonNodes();
                break;
            case 'search':
                toggleSearchBar(button, jsonSearchBar, jsonSearchInput, jsonOutput, () => originalJsonHTML);
                break;
            case 'copy':
                window.copyToClipboard('json-output');
                break;
        }
    });

    function handleJsonEscape(button) {
        const currentText = jsonOutput.dataset.plainText;
        if (!currentText) return;
        jsonOptions.isEscaped = !jsonOptions.isEscaped;
        button.classList.toggle('active');
        if (jsonOptions.isEscaped) {
            const escapedString = JSON.stringify(currentText);
            jsonOutput.innerHTML = `<span class="text-red-700">${escapedString}</span>`;
            jsonOutput.dataset.plainText = escapedString;
        } else {
            handleFormatJson();
        }
    }

    function toggleAllJsonNodes() {
        jsonOptions.isCollapsed = !jsonOptions.isCollapsed;
        const togglers = jsonOutput.querySelectorAll('.toggler > i');
        togglers.forEach(icon => {
            const content = icon.closest('.toggler').nextElementSibling;
            if (content) {
                if (jsonOptions.isCollapsed) {
                    content.style.display = 'none';
                    icon.classList.remove('fa-minus-square');
                    icon.classList.add('fa-plus-square');
                } else {
                    content.style.display = 'block';
                    icon.classList.remove('fa-plus-square');
                    icon.classList.add('fa-minus-square');
                }
            }
        });
    }

    // --- XML 工具逻辑 ---
    const formatXmlBtn = document.getElementById('format-xml-btn');
    const rawXmlInput = document.getElementById('raw-xml');
    const xmlOutput = document.getElementById('xml-output');
    const xmlMessageArea = document.getElementById('xml-message-area');
    const loadExampleXmlBtn = document.getElementById('load-example-xml');
    const xmlToolbar = document.getElementById('xml-toolbar');

    const xmlOptions = {
        compress: false,
    };

    let originalXmlHTML = '';

    const handleFormatXml = async () => {
        if (!rawXmlInput.value.trim()) {
            xmlOutput.innerHTML = '';
            xmlOutput.dataset.plainText = '';
            return;
        }
        xmlMessageArea.textContent = '';
        formatXmlBtn.disabled = true;
        formatXmlBtn.textContent = '处理中...';
        try {
            const response = await fetch('/api/v1/format-xml', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    raw_xml: rawXmlInput.value,
                    compress: xmlOptions.compress
                })
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);

            xmlOutput.innerHTML = result.xml_html;
            xmlOutput.dataset.plainText = result.xml_plain;
            originalXmlHTML = result.xml_html;
        } catch (error) {
            xmlMessageArea.textContent = `错误: ${error.message}`;
            xmlOutput.innerHTML = '';
        } finally {
            formatXmlBtn.disabled = false;
            formatXmlBtn.textContent = '格式化';
        }
    };

    formatXmlBtn.addEventListener('click', handleFormatXml);

    loadExampleXmlBtn.addEventListener('click', () => {
        rawXmlInput.value = MOCK_XML_DATA;
        handleFormatXml();
    });

    xmlToolbar.addEventListener('click', (e) => {
        const button = e.target.closest('.tool-btn');
        if (!button) return;
        const action = button.dataset.action;

        switch (action) {
            case 'compress':
                xmlOptions.compress = !xmlOptions.compress;
                button.classList.toggle('active');
                handleFormatXml();
                break;
            case 'copy':
                window.copyToClipboard('xml-output');
                break;
            default:
                 alert(`XML的 ${action} 功能待开发`);
                 break;
        }
    });

    // ... (文件其余部分代码保持不变) ...

// --- HTML 格式化工具逻辑 ---
const htmlFormatButton = document.getElementById('format-html-button');
const htmlRawInput = document.getElementById('raw-html-input');
const htmlOutputArea = document.getElementById('html-output');
const htmlMsgArea = document.getElementById('html-message-area');
const htmlLoadExampleBtn = document.getElementById('load-example-html-btn') || document.getElementById('load-example-html'); // 兼容两种可能的ID
const htmlToolBar = document.getElementById('html-toolbar');

const MOCK_HTML_DATA = `<!DOCTYPE html><html><head><title>一个示例</title><style>body { font-family: sans-serif; }</style></head><body><h1>标题</h1><p>这是一个段落, <b>部分加粗</b>。</p><ul><li>项目一</li><li>项目二</li></ul></body></html>`;

const handleFormatHtmlAction = async () => {
    console.log("--- 1. [JS] handleFormatHtmlAction 函数被触发 ---");

    if (!htmlRawInput || !htmlRawInput.value.trim()) {
        if(htmlOutputArea) {
            htmlOutputArea.innerHTML = '';
            htmlOutputArea.dataset.plainText = '';
        }
        return;
    }
    const payload = { raw_html: htmlRawInput.value };
    console.log("--- 2. [JS] 准备发送给后端的数据:", payload);

    if(htmlMsgArea) htmlMsgArea.textContent = '';
    if(htmlFormatButton) {
        htmlFormatButton.disabled = true;
        htmlFormatButton.textContent = '处理中...';
    }
    try {
        const response = await fetch('/api/v1/format-html', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });
        console.log("--- 3. [JS] 从后端收到原始响应:", response);

        const result = await response.json();
        console.log("--- 4. [JS] 解析后端JSON后的数据:", result);

        if (result.error) throw new Error(result.error);

        if(htmlOutputArea) {
            console.log("--- 5. [JS] 准备将以下HTML字符串应用到 innerHTML:", result.html_output);
            htmlOutputArea.innerHTML = result.html_output;
            htmlOutputArea.dataset.plainText = result.plain_output;
        }
    } catch (error) {
        console.error("--- [JS] 前端捕获到错误:", error);
        if(htmlMsgArea) htmlMsgArea.textContent = `错误: ${error.message}`;
        if(htmlOutputArea) htmlOutputArea.innerHTML = '';
    } finally {
        if(htmlFormatButton) {
            htmlFormatButton.disabled = false;
            htmlFormatButton.textContent = '格式化';
        }
    }
};

if (htmlFormatButton) {
    htmlFormatButton.addEventListener('click', handleFormatHtmlAction);
}
if (htmlLoadExampleBtn) {
    htmlLoadExampleBtn.addEventListener('click', () => {
        if(htmlRawInput) htmlRawInput.value = MOCK_HTML_DATA;
        handleFormatHtmlAction();
    });
}
if (htmlToolBar) {
    htmlToolBar.addEventListener('click', (e) => {
        const button = e.target.closest('.tool-btn');
        if (!button) return;
        const action = button.dataset.action;
        if (action === 'copy') {
            window.copyToClipboard('html-output');
        }
    });
}
// --- HTML 渲染工具逻辑 ---
    const renderHtmlBtn = document.getElementById('render-html-btn');
    const htmlRenderInput = document.getElementById('html-render-input');
    const htmlRenderOutputIframe = document.getElementById('html-render-output');
    const loadExampleRenderBtn = document.getElementById('load-example-render-btn');

    // 提供一个更适合渲染测试的示例
    const MOCK_RENDER_HTML_DATA = `<h1>这是一个一级标题</h1>
<p>这是一个段落，包含 <strong>加粗</strong> 和 <em>斜体</em> 文本。</p>
<p style="color: blue;">这是一个带有内联样式的蓝色段落。</p>
<ul>
    <li>列表项 1</li>
    <li>列表项 2</li>
</ul>
<p>下面是一个图片示例：</p>
<img src="https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png" alt="Google Logo" width="136">
<hr>
<blockquote>这是一个引用块。</blockquote>`;

    const handleHtmlRender = () => {
        // 安全检查，确保元素存在
        if (!htmlRenderInput || !htmlRenderOutputIframe) {
            console.error("HTML渲染工具的必要元素未找到。");
            return;
        }

        const rawHtml = htmlRenderInput.value;

        // 使用 srcdoc 属性来安全地设置iframe的内容
        // 这是现代浏览器推荐的方式，可以有效防止XSS攻击
        htmlRenderOutputIframe.srcdoc = rawHtml;
    };

    // 为按钮绑定事件，并添加安全检查
    if (renderHtmlBtn) {
        renderHtmlBtn.addEventListener('click', handleHtmlRender);
    }

    if (loadExampleRenderBtn) {
        loadExampleRenderBtn.addEventListener('click', () => {
            if (htmlRenderInput) {
                htmlRenderInput.value = MOCK_RENDER_HTML_DATA;
                // 载入示例后自动渲染一次
                handleHtmlRender();
            }
        });
    }

    // --- URL参数提取工具逻辑 ---
    const parseUrlBtn = document.getElementById('parse-url-btn');
    const rawUrlInput = document.getElementById('raw-url-input');
    const baseUrlOutput = document.getElementById('base-url-output');
    const paramsOutput = document.getElementById('params-output');
    const urlMessageArea = document.getElementById('url-message-area');
    const loadExampleUrlBtn = document.getElementById('load-example-url-btn');
    const urlToolContainer = document.getElementById('url-tool');

    const MOCK_URL_DATA = `https://www.google.com/search?q=x&oq=X+&gs_lcrp=EgZjaHJvbWUqBggAEEUYOzIGCAAQRRg7MgYIARBFGDwyBggCEEUYPDIGCAMQRRg8MgYIBBBFGDwyBggFEEUYQTIGCAYQRRg8MgYIBxBFGDzSAQc3NDlqMGo3qAIIsAIB8QXeVyjRzSytS_EF3lco0c0srUs&sourceid=chrome&ie=UTF-8`;

    const handleParseUrl = async () => {
        if (!rawUrlInput || !rawUrlInput.value.trim()) return;

        if (urlMessageArea) urlMessageArea.textContent = '';
        if (parseUrlBtn) {
            parseUrlBtn.disabled = true;
            parseUrlBtn.textContent = '提取中...';
        }

        try {
            const response = await fetch('/api/v1/parse-url', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ raw_url: rawUrlInput.value })
            });

            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);

            if (baseUrlOutput) {
                baseUrlOutput.innerHTML = result.base_url_html;
                baseUrlOutput.dataset.plainText = result.base_url_plain;
            }
            if (paramsOutput) {
                paramsOutput.innerHTML = result.params_html;
                paramsOutput.dataset.plainText = result.params_plain;
            }

        } catch (error) {
            if (urlMessageArea) urlMessageArea.textContent = `错误: ${error.message}`;
        } finally {
            if (parseUrlBtn) {
                parseUrlBtn.disabled = false;
                parseUrlBtn.textContent = '提取参数';
            }
        }
    };

    if (parseUrlBtn) {
        parseUrlBtn.addEventListener('click', handleParseUrl);
    }

    if (loadExampleUrlBtn) {
        loadExampleUrlBtn.addEventListener('click', () => {
            if (rawUrlInput) rawUrlInput.value = MOCK_URL_DATA;
            handleParseUrl();
        });
    }

    if (urlToolContainer) {
        urlToolContainer.addEventListener('click', (e) => {
            const copyButton = e.target.closest('.copy-btn');
            if (copyButton) {
                const targetId = copyButton.dataset.copyTarget;
                if (targetId) {
                    window.copyToClipboard(targetId);
                }
            }
        });
    }
    // --- Base64 编码解码工具逻辑 ---
    const base64EncodeBtn = document.getElementById('base64-encode-btn');
    const base64DecodeBtn = document.getElementById('base64-decode-btn');
    const base64ClearBtn = document.getElementById('base64-clear-btn');
    const base64PlainInput = document.getElementById('base64-plain-input');
    const base64B64Input = document.getElementById('base64-b64-input');
    const base64MessageArea = document.getElementById('base64-message-area');
    const base64ToolContainer = document.getElementById('base64-tool');

    async function handleBase64Action(action) {
        let endpoint, payload, inputElement, outputElement;

        if (action === 'encode') {
            endpoint = '/api/v1/base64/encode';
            inputElement = base64PlainInput;
            outputElement = base64B64Input;
            payload = { plain_text: inputElement.value };
        } else {
            endpoint = '/api/v1/base64/decode';
            inputElement = base64B64Input;
            outputElement = base64PlainInput;
            payload = { b64_string: inputElement.value };
        }

        if (!inputElement || !inputElement.value.trim()) return;

        if (base64MessageArea) base64MessageArea.textContent = '';
        const btn = (action === 'encode') ? base64EncodeBtn : base64DecodeBtn;
        if(btn) {
            btn.disabled = true;
            btn.classList.add('opacity-50');
        }

        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);
            if (outputElement) outputElement.value = result.result;
        } catch (error) {
            if (base64MessageArea) base64MessageArea.textContent = `错误: ${error.message}`;
        } finally {
            if(btn) {
                btn.disabled = false;
                btn.classList.remove('opacity-50');
            }
        }
    }

    if (base64EncodeBtn) {
        base64EncodeBtn.addEventListener('click', () => handleBase64Action('encode'));
    }

    if (base64DecodeBtn) {
        base64DecodeBtn.addEventListener('click', () => handleBase64Action('decode'));
    }

    if (base64ClearBtn) {
        base64ClearBtn.addEventListener('click', () => {
            if(base64PlainInput) base64PlainInput.value = '';
            if(base64B64Input) base64B64Input.value = '';
            if(base64MessageArea) base64MessageArea.textContent = '';
        });
    }

    if (base64ToolContainer) {
        // 使用事件委托处理两个复制按钮
        base64ToolContainer.addEventListener('click', (e) => {
            const copyButton = e.target.closest('.copy-btn');
            if (copyButton) {
                const targetId = copyButton.dataset.copyTarget;
                const targetTextarea = document.getElementById(targetId);
                if (targetTextarea && targetTextarea.value) {
                    navigator.clipboard.writeText(targetTextarea.value)
                        .then(window.showCopyModal)
                        .catch(err => console.error('复制失败: ', err));
                }
            }
        });
    }



    // --- 通用函数 ---
    window.showCopyModal = function() {
        copyModal.classList.remove('hidden', 'opacity-0');
        setTimeout(() => {
            copyModal.classList.add('opacity-0');
            setTimeout(() => copyModal.classList.add('hidden'), 300);
        }, 1500);
    };

    window.copyToClipboard = function(elementId) {
        const element = document.getElementById(elementId);
        if (!element) {
            console.error(`复制失败: 未找到元素 #${elementId}`);
            return;
        }
        const textToCopy = element.dataset.plainText;
        if (textToCopy === undefined || textToCopy === null) {
            console.error(`复制失败: 元素 #${elementId} 没有 "data-plain-text" 属性。`);
            return;
        }
        navigator.clipboard.writeText(textToCopy).then(window.showCopyModal).catch(err => {
             console.error('复制失败: ', err);
             alert('复制失败!');
        });
    };

    function highlightTextInNode(node, searchTerm) {
        if (searchTerm.trim() === '') return;
        const safeSearchTerm = searchTerm.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
        if (!safeSearchTerm) return;
        const regex = new RegExp(safeSearchTerm, 'gi');

        const walker = document.createTreeWalker(node, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        const nodesToProcess = [];
        while(textNode = walker.nextNode()) {
            nodesToProcess.push(textNode);
        }

        nodesToProcess.forEach(textNode => {
            const text = textNode.nodeValue;
            if (regex.test(text)) {
                const fragment = document.createDocumentFragment();
                let lastIndex = 0;
                text.replace(regex, (match, offset) => {
                    fragment.appendChild(document.createTextNode(text.substring(lastIndex, offset)));
                    const mark = document.createElement('mark');
                    mark.className = 'search-highlight';
                    mark.textContent = match;
                    fragment.appendChild(mark);
                    lastIndex = offset + match.length;
                });
                fragment.appendChild(document.createTextNode(text.substring(lastIndex)));
                textNode.parentNode.replaceChild(fragment, textNode);
            }
        });
    }

    function toggleSearchBar(button, searchBar, searchInput, outputElement, getOriginalHTML) {
        const isHidden = searchBar.classList.contains('hidden');
        button.classList.toggle('active', isHidden);
        if (isHidden) {
            searchBar.classList.remove('hidden');
            searchInput.focus();
            if(!getOriginalHTML()) {
                 if(outputElement.id === 'json-output') originalJsonHTML = outputElement.innerHTML;
            }
        } else {
            searchBar.classList.add('hidden');
            outputElement.innerHTML = getOriginalHTML();
        }
    }

    jsonSearchClear.addEventListener('click', () => {
        jsonSearchInput.value = '';
        if (originalJsonHTML) {
            jsonOutput.innerHTML = originalJsonHTML;
        }
    });

    jsonSearchInput.addEventListener('input', () => {
        if (!originalJsonHTML) return;
        jsonOutput.innerHTML = originalJsonHTML;
        highlightTextInNode(jsonOutput, jsonSearchInput.value);
    });

    document.querySelector('main').addEventListener('click', (e) => {
        const toggler = e.target.closest('.toggler');
        if (!toggler) return;
        const icon = toggler.querySelector('i');
        const content = toggler.nextElementSibling;
        if (content && icon) {
            const isHidden = content.style.display === 'none';
            content.style.display = isHidden ? 'block' : 'none';
            icon.classList.toggle('fa-plus-square', !isHidden);
            icon.classList.toggle('fa-minus-square', isHidden);
        }
    });

    // --- 初始化 ---
    document.querySelector('.sidebar-link.active')?.click();
});