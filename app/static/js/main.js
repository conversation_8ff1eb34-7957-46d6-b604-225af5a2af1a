document.addEventListener('DOMContentLoaded', () => {
    // --- 示例数据 ---
    const MOCK_HEADERS_DATA = `Host: www.example.com
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8
Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2
Connection: keep-alive
Cookie: session_id=abc123xyz456; theme=dark`;

    const MOCK_JSON_DATA = `{
    "user_id": 12345, "username": "爬虫爱好者", "is_active": true,
    "roles": ["admin", "editor"],
    "profile": {"email": "<EMAIL>", "address": null, "preferences": {"theme": "dark", "notifications": {"email": true, "sms": false}}},
    "logs": [{"timestamp": 1672531200, "action": "login"}, {"timestamp": 1672534800, "action": "update_profile"}]
}`;

    const MOCK_SORTABLE_JSON_DATA = `{
        "zebra": 1, "apple": 2, "metadata": { "version": "1.0", "author": "tester", "timestamp": "2025-01-01" },
        "data": [ {"id": 10, "name": "Xylophone"}, {"id": 2, "name": "Tambourine"} ], "cat": 3
    }`;

    const MOCK_XML_DATA = `<?xml version="1.0" encoding="UTF-8"?><bookstore><book category="cooking"><title lang="en">Everyday Italian</title><author>Giada De Laurentiis</author><year>2005</year><price>30.00</price></book><book category="children"><title lang="en">Harry Potter</title><author>J K. Rowling</author><year>2005</year><price>29.99</price></book></bookstore>`;

    const MOCK_HTML_DATA = `<!DOCTYPE html><html><head><title>一个示例</title><style>body { font-family: sans-serif; }</style></head><body><h1>标题</h1><p>这是一个段落, <b>部分加粗</b>。</p><ul><li>项目一</li><li>项目二</li></ul></body></html>`;

    const MOCK_RENDER_HTML_DATA = `<h1>这是一个一级标题</h1>
<p>这是一个段落，包含 <strong>加粗</strong> 和 <em>斜体</em> 文本。</p>
<p style="color: blue;">这是一个带有内联样式的蓝色段落。</p>
<ul>
    <li>列表项 1</li>
    <li>列表项 2</li>
</ul>
<p>下面是一个图片示例：</p>
<img src="https://news-bos.cdn.bcebos.com/mvideo/log-news.png" alt="Google Logo" width="136">
<hr>
<a href="https://news.baidu.com/"> 百度新闻</a>
<blockquote>这是一个引用块。</blockquote>`;

    const MOCK_URL_DATA = `https://example.com/path?param1=value1&param2=value2`;

    const MOCK_TEXT_A = `function hello() {
    console.log("Hello World");
    return true;
}

const user = {
    name: "张三",
    age: 25,
    city: "北京"
};

// 这是一个注释
let count = 0;`;

    const MOCK_TEXT_B = `function hello(name) {
    console.log("Hello " + name);
    console.log("Welcome!");
    return true;
}

const user = {
    name: "李四",
    age: 28,
    city: "上海",
    email: "<EMAIL>"
};

// 这是修改后的注释
let count = 10;
let isActive = true;`;

    // 编码解码工具示例数据
    const ENCODING_EXAMPLES = {
        // 字符编码检测示例
        encoding: `这是一个包含中文的测试文本。
English text with Chinese characters: 你好世界
Special symbols: ©®™€£¥
Emoji: 🌍🚀💻`,

        // Base64编码示例
        base64: {
            original: `Hello 世界! 这是Base64编码测试。
包含中文、英文、特殊字符：©®™
爬虫工具箱 - Spider Tools`,
            encoded: `SGVsbG8g5LiW55WMISDovozkuKpCYXNlNjTnvJbnoIHmtYvor5XjgII=
5YyF5ZCr5Lit5paH44CB6Iux5paH44CB54m55q6K5a2X56ym77yM4pWp4pSu4oSi
54is6Jmr5bel5YW35LitIC0gU3BpZGVyIFRvb2xz`
        },

        // HTML实体编码示例
        htmlEntity: {
            original: `<div class="container">
    <h1>欢迎使用爬虫工具箱</h1>
    <p>这里有特殊字符：&、<、>、"、'</p>
    <a href="https://example.com?name=张三&age=25">链接示例</a>
</div>`,
            encoded: `&lt;div class=&quot;container&quot;&gt;
    &lt;h1&gt;欢迎使用爬虫工具箱&lt;/h1&gt;
    &lt;p&gt;这里有特殊字符：&amp;、&lt;、&gt;、&quot;、&#39;&lt;/p&gt;
    &lt;a href=&quot;https://example.com?name=张三&amp;age=25&quot;&gt;链接示例&lt;/a&gt;
&lt;/div&gt;`
        },

        // URL编码示例
        urlEncode: {
            original: `https://www.example.com/search?q=Python爬虫教程&category=编程技术&author=张三`,
            encoded: `https://www.example.com/search?q=Python%E7%88%AC%E8%99%AB%E6%95%99%E7%A8%8B&category=%E7%BC%96%E7%A8%8B%E6%8A%80%E6%9C%AF&author=%E5%BC%A0%E4%B8%89`
        },

        // Unicode编码示例
        unicode: {
            original: `Hello 世界! 🌍
这是Unicode编码测试
特殊符号：©®™€£¥`,
            encoded: `Hello \\u4e16\\u754c! \\ud83c\\udf0d
\\u8fd9\\u662fUnicode\\u7f16\\u7801\\u6d4b\\u8bd5
\\u7279\\u6b8a\\u7b26\\u53f7\\uff1a\\u00a9\\u00ae\\u2122\\u20ac\\u00a3\\u00a5`
        }
    };


    // --- 元素获取 ---
    const sidebarNav = document.getElementById('sidebar-nav');
    const toolContents = document.querySelectorAll('.tool-content');
    const copyModal = document.getElementById('copy-modal');

    // --- 页面路由/工具切换逻辑 ---
    if (sidebarNav) {
        sidebarNav.addEventListener('click', (e) => {
            e.preventDefault();
            const targetLink = e.target.closest('.sidebar-link');
            if (!targetLink) return;
            sidebarNav.querySelectorAll('.sidebar-link').forEach(link => link.classList.remove('active'));
            targetLink.classList.add('active');
            const toolId = targetLink.dataset.tool;
            toolContents.forEach(content => {
                content.id === toolId ? content.classList.remove('hidden') : content.classList.add('hidden');
            });
            // 当切换到时间转换工具时，执行其初始化
            if (toolId === 'timestamp-tool') {
                setTimeout(initTimestampTool, 100);
            } else if (toolId === 'date-calculator-tool') {
                setTimeout(initDateCalculatorTool, 100);
            }
        });
    }


    // --- Headers 工具逻辑 ---
    const convertHeadersBtn = document.getElementById('convert-headers-btn');
    const clearHeadersBtn = document.getElementById('clear-headers-btn');
    const rawHeadersInput = document.getElementById('raw-headers');
    const headersOutput = document.getElementById('headers-output');
    const cookiesOutput = document.getElementById('cookies-output');
    const headersMessageArea = document.getElementById('headers-message-area');
    const headersInputMessageArea = document.getElementById('headers-input-message-area');
    const loadExampleHeadersBtn = document.getElementById('load-example-headers');

    // Headers清空按钮功能
    if (clearHeadersBtn) {
        clearHeadersBtn.addEventListener('click', () => {
            if (rawHeadersInput) rawHeadersInput.value = '';
            if (headersOutput) {
                headersOutput.innerHTML = '';
                headersOutput.dataset.plainText = '';
            }
            if (cookiesOutput) {
                cookiesOutput.innerHTML = '';
                cookiesOutput.dataset.plainText = '';
            }
            if (headersMessageArea) headersMessageArea.textContent = '';
            if (headersInputMessageArea) headersInputMessageArea.textContent = '';
            if (headersInputMessageArea) headersInputMessageArea.textContent = '已清空所有内容';
            setTimeout(() => {
                if (headersInputMessageArea) headersInputMessageArea.textContent = '';
            }, 2000);
            console.log('Headers工具已清空');
        });
    }

    if (loadExampleHeadersBtn) {
        loadExampleHeadersBtn.addEventListener('click', () => {
            if (rawHeadersInput) rawHeadersInput.value = MOCK_HEADERS_DATA;
            if (headersInputMessageArea) headersInputMessageArea.textContent = '已载入示例数据';
            setTimeout(() => {
                if (headersInputMessageArea) headersInputMessageArea.textContent = '';
            }, 2000);
            if (convertHeadersBtn) convertHeadersBtn.click();
        });
    }
    if (convertHeadersBtn) {
        convertHeadersBtn.addEventListener('click', async () => {
            if (headersMessageArea) headersMessageArea.textContent = '';
            convertHeadersBtn.disabled = true;
            convertHeadersBtn.innerHTML = '<i class="fas fa-spinner fa-spin w-4 h-4 mr-1"></i>转换中...';
            try {
                const response = await fetch('/api/v1/convert-headers', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({raw_headers: rawHeadersInput.value})
                });
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                const result = await response.json();
                if (result.error) throw new Error(result.error);
                if (headersOutput) {
                    headersOutput.innerHTML = result.headers_html;
                    headersOutput.dataset.plainText = result.headers_plain;
                }
                if (cookiesOutput) {
                    cookiesOutput.innerHTML = result.cookies_html;
                    cookiesOutput.dataset.plainText = result.cookies_plain;
                }
            } catch (error) {
                if (headersMessageArea) headersMessageArea.textContent = `错误: ${error.message}`;
            } finally {
                convertHeadersBtn.disabled = false;
                convertHeadersBtn.innerHTML = '<i class="fas fa-magic w-4 h-4 mr-1"></i>转换格式';
            }
        });
    }
    const headersToolContainer = document.getElementById('headers-tool');
    if (headersToolContainer) {
        headersToolContainer.addEventListener('click', (e) => {
            const button = e.target.closest('[data-action]');
            if (button) {
                const action = button.dataset.action;
                switch (action) {
                    case 'copy-headers':
                        window.copyToClipboard('headers-output');
                        break;
                    case 'copy-cookies':
                        window.copyToClipboard('cookies-output');
                        break;
                }
            }
        });
    }

 // --- JSON 工具逻辑 ---
    const formatJsonBtn = document.getElementById('format-json-btn');
    const clearJsonBtn = document.getElementById('clear-json-btn');
    const rawJsonInput = document.getElementById('raw-json');
    const jsonOutput = document.getElementById('json-output');
    const jsonMessageArea = document.getElementById('json-message-area');
    const jsonInputMessageArea = document.getElementById('json-input-message-area');
    const loadExampleJsonBtn = document.getElementById('load-example-json');
    const loadSortableJsonBtn = document.getElementById('load-sortable-json');
    const jsonToolbar = document.getElementById('json-toolbar');
    // const jsonSearchBar = document.getElementById('json-search-bar');
    // const jsonSearchInput = document.getElementById('json-search-input');
    // const jsonSearchClear = document.getElementById('json-search-clear');


    const jsonOptions = {
        sort_keys: false,
        compress: false,
        isEscaped: false,
        isCollapsed: false,
    };

    let originalJsonHTML = '';

    const handleFormatJson = async () => {
        if (!rawJsonInput.value.trim()) {
            jsonOutput.innerHTML = '';
            jsonOutput.dataset.plainText = '';
            return;
        }
        jsonMessageArea.textContent = '';
        formatJsonBtn.disabled = true;
        formatJsonBtn.innerHTML = '<i class="fas fa-spinner fa-spin w-4 h-4 mr-1"></i>处理中...';
        try {
            const response = await fetch('/api/v1/format-json', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    raw_json: rawJsonInput.value,
                    sort_keys: jsonOptions.sort_keys,
                    compress: jsonOptions.compress
                })
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);

            jsonOutput.innerHTML = result.json_html;
            jsonOutput.dataset.plainText = result.json_plain;
            originalJsonHTML = result.json_html;
        } catch (error) {
            jsonMessageArea.textContent = `错误: ${error.message}`;
            jsonOutput.innerHTML = '';
        } finally {
            formatJsonBtn.disabled = false;
            formatJsonBtn.innerHTML = '<i class="fas fa-magic w-4 h-4 mr-1"></i>格式化';
        }
    };

    formatJsonBtn.addEventListener('click', handleFormatJson);

    // 清空按钮功能
    if (clearJsonBtn) {
        clearJsonBtn.addEventListener('click', () => {
            rawJsonInput.value = '';
            jsonOutput.innerHTML = '';
            jsonOutput.dataset.plainText = '';
            jsonMessageArea.textContent = '';
            if (jsonInputMessageArea) jsonInputMessageArea.textContent = '';
            // 重置所有选项状态
            jsonOptions.sort_keys = false;
            jsonOptions.compress = false;
            jsonOptions.isEscaped = false;
            jsonOptions.isCollapsed = false;
            // 重置工具栏按钮状态
            jsonToolbar.querySelectorAll('.tool-btn.active').forEach(btn => btn.classList.remove('active'));
            if (jsonInputMessageArea) jsonInputMessageArea.textContent = '已清空所有内容';
            setTimeout(() => {
                if (jsonInputMessageArea) jsonInputMessageArea.textContent = '';
            }, 2000);
            console.log('JSON工具已清空');
        });
    }

    loadExampleJsonBtn.addEventListener('click', () => {
        rawJsonInput.value = MOCK_JSON_DATA;
        if (jsonInputMessageArea) jsonInputMessageArea.textContent = '已载入常规示例数据';
        setTimeout(() => {
            if (jsonInputMessageArea) jsonInputMessageArea.textContent = '';
        }, 2000);
        handleFormatJson();
    });
    loadSortableJsonBtn.addEventListener('click', () => {
        rawJsonInput.value = MOCK_SORTABLE_JSON_DATA;
        if (jsonInputMessageArea) jsonInputMessageArea.textContent = '已载入排序示例数据';
        setTimeout(() => {
            if (jsonInputMessageArea) jsonInputMessageArea.textContent = '';
        }, 2000);
        if (!jsonOptions.sort_keys) {
            jsonToolbar.querySelector('[data-action="sort"]').click();
        } else {
            handleFormatJson();
        }
    });

    jsonToolbar.addEventListener('click', (e) => {
        const button = e.target.closest('.tool-btn');
        if (!button) return;
        const action = button.dataset.action;

        switch (action) {
            case 'sort':
            case 'compress':
                jsonOptions[action] = !jsonOptions[action];
                button.classList.toggle('active');
                handleFormatJson();
                break;
            case 'escape':
                handleJsonEscape(button);
                break;
            case 'collapse':
                toggleAllJsonNodes();
                break;
           case 'search':
                toggleSearchBar('json'); // 传入工具前缀
                break;
            case 'copy':
                window.copyToClipboard('json-output');
                break;
        }
    });

    // JSON折叠/展开功能 - 新的交互式折叠
    if (jsonOutput) {
        jsonOutput.addEventListener('click', (e) => {
            const toggle = e.target.closest('.json-toggle');
            if (toggle) {
                const targetId = toggle.dataset.target;
                const content = document.getElementById(targetId);
                const icon = toggle.querySelector('.json-toggle-icon');

                if (content && icon) {
                    const isCollapsed = content.classList.contains('collapsed');

                    if (isCollapsed) {
                        // 展开
                        content.classList.remove('collapsed');
                        icon.classList.remove('fa-plus-square');
                        icon.classList.add('fa-minus-square');
                    } else {
                        // 折叠
                        content.classList.add('collapsed');
                        icon.classList.remove('fa-minus-square');
                        icon.classList.add('fa-plus-square');
                    }
                }
            }
        });
    }

    function handleJsonEscape(button) {
        const currentText = jsonOutput.dataset.plainText;
        if (!currentText) return;
        jsonOptions.isEscaped = !jsonOptions.isEscaped;
        button.classList.toggle('active');
        if (jsonOptions.isEscaped) {
            const escapedString = JSON.stringify(currentText);
            jsonOutput.innerHTML = `<span class="text-red-700">${escapedString}</span>`;
            jsonOutput.dataset.plainText = escapedString;
        } else {
            handleFormatJson();
        }
    }

    function toggleAllJsonNodes() {
        jsonOptions.isCollapsed = !jsonOptions.isCollapsed;

        // 新的折叠系统：查找所有json-toggle元素
        const toggles = jsonOutput.querySelectorAll('.json-toggle');
        toggles.forEach(toggle => {
            const targetId = toggle.dataset.target;
            const content = document.getElementById(targetId);
            const icon = toggle.querySelector('.json-toggle-icon');

            if (content && icon) {
                if (jsonOptions.isCollapsed) {
                    // 全部折叠
                    content.classList.add('collapsed');
                    icon.classList.remove('fa-minus-square');
                    icon.classList.add('fa-plus-square');
                } else {
                    // 全部展开
                    content.classList.remove('collapsed');
                    icon.classList.remove('fa-plus-square');
                    icon.classList.add('fa-minus-square');
                }
            }
        });
    }

    // --- XML 工具逻辑 ---
    const formatXmlBtn = document.getElementById('format-xml-btn');
    const clearXmlBtn = document.getElementById('clear-xml-btn');
    const rawXmlInput = document.getElementById('raw-xml');
    const xmlOutput = document.getElementById('xml-output');
    const xmlMessageArea = document.getElementById('xml-message-area');
    const xmlInputMessageArea = document.getElementById('xml-input-message-area');
    const loadExampleXmlBtn = document.getElementById('load-example-xml');
    const xmlToolbar = document.getElementById('xml-toolbar');
    const xmlOptions = {compress: false};
    let originalXmlHTML = '';

    const handleFormatXml = async () => {
        if (!rawXmlInput || !rawXmlInput.value.trim()) {
            if (xmlOutput) {
                xmlOutput.innerHTML = '';
                xmlOutput.dataset.plainText = '';
            }
            return;
        }
        if (xmlMessageArea) xmlMessageArea.textContent = '';
        if (formatXmlBtn) {
            formatXmlBtn.disabled = true;
            formatXmlBtn.innerHTML = '<i class="fas fa-spinner fa-spin w-4 h-4 mr-1"></i>处理中...';
        }
        try {
            const response = await fetch('/api/v1/format-xml', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({raw_xml: rawXmlInput.value, compress: xmlOptions.compress})
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);
            if (xmlOutput) {
                xmlOutput.innerHTML = result.xml_html;
                xmlOutput.dataset.plainText = result.xml_plain;
                originalXmlHTML = result.xml_html;
            }
        } catch (error) {
            if (xmlMessageArea) xmlMessageArea.textContent = `错误: ${error.message}`;
            if (xmlOutput) xmlOutput.innerHTML = '';
        } finally {
            if (formatXmlBtn) {
                formatXmlBtn.disabled = false;
                formatXmlBtn.innerHTML = '<i class="fas fa-magic w-4 h-4 mr-1"></i>格式化';
            }
        }
    };

    if (formatXmlBtn) formatXmlBtn.addEventListener('click', handleFormatXml);

    // XML清空按钮功能
    if (clearXmlBtn) {
        clearXmlBtn.addEventListener('click', () => {
            if (rawXmlInput) rawXmlInput.value = '';
            if (xmlOutput) {
                xmlOutput.innerHTML = '';
                xmlOutput.dataset.plainText = '';
            }
            if (xmlMessageArea) xmlMessageArea.textContent = '';
            if (xmlInputMessageArea) xmlInputMessageArea.textContent = '';
            // 重置XML选项状态
            xmlOptions.compress = false;
            // 重置工具栏按钮状态
            if (xmlToolbar) xmlToolbar.querySelectorAll('.tool-btn.active').forEach(btn => btn.classList.remove('active'));
            if (xmlInputMessageArea) xmlInputMessageArea.textContent = '已清空所有内容';
            setTimeout(() => {
                if (xmlInputMessageArea) xmlInputMessageArea.textContent = '';
            }, 2000);
            console.log('XML工具已清空');
        });
    }

    if (loadExampleXmlBtn) loadExampleXmlBtn.addEventListener('click', () => {
        if (rawXmlInput) rawXmlInput.value = MOCK_XML_DATA;
        if (xmlInputMessageArea) xmlInputMessageArea.textContent = '已载入示例数据';
        setTimeout(() => {
            if (xmlInputMessageArea) xmlInputMessageArea.textContent = '';
        }, 2000);
        handleFormatXml();
    });
    if (xmlToolbar) xmlToolbar.addEventListener('click', (e) => {
        const button = e.target.closest('.tool-btn');
        if (!button) return;
        const action = button.dataset.action;
        switch (action) {
            case 'compress':
                xmlOptions.compress = !xmlOptions.compress;
                button.classList.toggle('active');
                handleFormatXml();
                break;
          // (新添加) START: XML搜索逻辑
            case 'search':
                toggleSearchBar('xml'); // 传入工具前缀
                break;
            case 'copy':
                window.copyToClipboard('xml-output');
                break;
            default:
                break;
        }
    });

    // --- HTML 格式化工具逻辑 ---
    const htmlFormatButton = document.getElementById('format-html-button');
    const clearHtmlBtn = document.getElementById('clear-html-btn');
    const htmlRawInput = document.getElementById('raw-html-input');
    const htmlOutputArea = document.getElementById('html-output');
    const htmlMsgArea = document.getElementById('html-message-area');
    const htmlInputMessageArea = document.getElementById('html-input-message-area');
    const htmlLoadExampleBtn = document.getElementById('load-example-html-btn');
    const htmlToolBar = document.getElementById('html-toolbar');

    const handleFormatHtmlAction = async () => {
        if (!htmlRawInput || !htmlRawInput.value.trim()) {
            if (htmlOutputArea) {
                htmlOutputArea.innerHTML = '';
                htmlOutputArea.dataset.plainText = '';
            }
            return;
        }
        if (htmlMsgArea) htmlMsgArea.textContent = '';
        if (htmlFormatButton) {
            htmlFormatButton.disabled = true;
            htmlFormatButton.innerHTML = '<i class="fas fa-spinner fa-spin w-4 h-4 mr-1"></i>处理中...';
        }
        try {
            const response = await fetch('/api/v1/format-html', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({raw_html: htmlRawInput.value})
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);
            if (htmlOutputArea) {
                htmlOutputArea.innerHTML = result.html_output;
                htmlOutputArea.dataset.plainText = result.plain_output;
            }
        } catch (error) {
            if (htmlMsgArea) htmlMsgArea.textContent = `错误: ${error.message}`;
            if (htmlOutputArea) htmlOutputArea.innerHTML = '';
        } finally {
            if (htmlFormatButton) {
                htmlFormatButton.disabled = false;
                htmlFormatButton.innerHTML = '<i class="fas fa-magic w-4 h-4 mr-1"></i>格式化';
            }
        }
    };

    if (htmlFormatButton) htmlFormatButton.addEventListener('click', handleFormatHtmlAction);

    // HTML清空按钮功能
    if (clearHtmlBtn) {
        clearHtmlBtn.addEventListener('click', () => {
            if (htmlRawInput) htmlRawInput.value = '';
            if (htmlOutputArea) {
                htmlOutputArea.innerHTML = '';
                htmlOutputArea.dataset.plainText = '';
            }
            if (htmlMsgArea) htmlMsgArea.textContent = '';
            if (htmlInputMessageArea) htmlInputMessageArea.textContent = '';
            if (htmlInputMessageArea) htmlInputMessageArea.textContent = '已清空所有内容';
            setTimeout(() => {
                if (htmlInputMessageArea) htmlInputMessageArea.textContent = '';
            }, 2000);
            console.log('HTML工具已清空');
        });
    }

    if (htmlLoadExampleBtn) {
        htmlLoadExampleBtn.addEventListener('click', () => {
            if (htmlRawInput) htmlRawInput.value = MOCK_HTML_DATA;
            if (htmlInputMessageArea) htmlInputMessageArea.textContent = '已载入示例数据';
            setTimeout(() => {
                if (htmlInputMessageArea) htmlInputMessageArea.textContent = '';
            }, 2000);
            handleFormatHtmlAction();
        });
    }
    if (htmlToolBar) {
        htmlToolBar.addEventListener('click', (e) => {
            const button = e.target.closest('.tool-btn');
            if (!button) return;
            const action = button.dataset.action;
            // 使用 switch 或多个 if 判断
            switch (action) {
                case 'copy':
                    window.copyToClipboard('html-output');
                    break;
                case 'search':
                    toggleSearchBar('html-format'); // 传入正确的工具前缀
                    break;
            }
        });
    }

    // --- HTML 渲染工具逻辑 ---
    const renderHtmlBtn = document.getElementById('render-html-btn');
    const clearHtmlRenderBtn = document.getElementById('clear-html-render-btn');
    const refreshRenderBtn = document.getElementById('refresh-render-btn');
    const htmlRenderInput = document.getElementById('html-render-input');
    const htmlRenderOutputIframe = document.getElementById('html-render-output');
    const htmlRenderInputMessageArea = document.getElementById('html-render-input-message-area');
    const htmlRenderMessageArea = document.getElementById('html-render-message-area');
    const loadExampleRenderBtn = document.getElementById('load-example-render-btn');

    const handleHtmlRender = () => {
        if (!htmlRenderInput || !htmlRenderOutputIframe) {
            console.error("HTML渲染工具的必要元素未找到。");
            return;
        }
        const rawHtml = htmlRenderInput.value;
        if (!rawHtml.trim()) {
            htmlRenderOutputIframe.srcdoc = '';
            if (htmlRenderMessageArea) htmlRenderMessageArea.textContent = '请输入HTML代码';
            return;
        }
        htmlRenderOutputIframe.srcdoc = rawHtml;
        if (htmlRenderMessageArea) htmlRenderMessageArea.textContent = '';
    };

    if (renderHtmlBtn) renderHtmlBtn.addEventListener('click', handleHtmlRender);

    // HTML渲染清空按钮功能
    if (clearHtmlRenderBtn) {
        clearHtmlRenderBtn.addEventListener('click', () => {
            if (htmlRenderInput) htmlRenderInput.value = '';
            if (htmlRenderOutputIframe) htmlRenderOutputIframe.srcdoc = '';
            if (htmlRenderMessageArea) htmlRenderMessageArea.textContent = '';
            if (htmlRenderInputMessageArea) htmlRenderInputMessageArea.textContent = '已清空所有内容';
            setTimeout(() => {
                if (htmlRenderInputMessageArea) htmlRenderInputMessageArea.textContent = '';
            }, 2000);
            console.log('HTML渲染工具已清空');
        });
    }

    // 刷新渲染按钮功能
    if (refreshRenderBtn) {
        refreshRenderBtn.addEventListener('click', () => {
            handleHtmlRender();
            if (htmlRenderInputMessageArea) htmlRenderInputMessageArea.textContent = '已刷新渲染';
            setTimeout(() => {
                if (htmlRenderInputMessageArea) htmlRenderInputMessageArea.textContent = '';
            }, 2000);
        });
    }

    if (loadExampleRenderBtn) {
        loadExampleRenderBtn.addEventListener('click', () => {
            if (htmlRenderInput) {
                htmlRenderInput.value = MOCK_RENDER_HTML_DATA;
                if (htmlRenderInputMessageArea) htmlRenderInputMessageArea.textContent = '已载入示例数据';
                setTimeout(() => {
                    if (htmlRenderInputMessageArea) htmlRenderInputMessageArea.textContent = '';
                }, 2000);
                handleHtmlRender();
            }
        });
    }

    // --- URL参数提取工具逻辑 ---
    const parseUrlBtn = document.getElementById('parse-url-btn');
    const clearUrlBtn = document.getElementById('clear-url-btn');
    const rawUrlInput = document.getElementById('raw-url-input');
    const baseUrlOutput = document.getElementById('base-url-output');
    const paramsOutput = document.getElementById('params-output');
    const urlMessageArea = document.getElementById('url-message-area');
    const urlInputMessageArea = document.getElementById('url-input-message-area');
    const loadExampleUrlBtn = document.getElementById('load-example-url-btn');
    const urlToolContainer = document.getElementById('url-tool');

    const handleParseUrl = async () => {
        if (!rawUrlInput || !rawUrlInput.value.trim()) return;
        if (urlMessageArea) urlMessageArea.textContent = '';
        if (parseUrlBtn) {
            parseUrlBtn.disabled = true;
            parseUrlBtn.innerHTML = '<i class="fas fa-spinner fa-spin w-4 h-4 mr-1"></i>提取中...';
        }
        try {
            const response = await fetch('/api/v1/parse-url', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({raw_url: rawUrlInput.value})
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);
            if (baseUrlOutput) {
                baseUrlOutput.innerHTML = result.base_url_html;
                baseUrlOutput.dataset.plainText = result.base_url_plain;
            }
            if (paramsOutput) {
                paramsOutput.innerHTML = result.params_html;
                paramsOutput.dataset.plainText = result.params_plain;
            }
        } catch (error) {
            if (urlMessageArea) urlMessageArea.textContent = `错误: ${error.message}`;
        } finally {
            if (parseUrlBtn) {
                parseUrlBtn.disabled = false;
                parseUrlBtn.innerHTML = '<i class="fas fa-magic w-4 h-4 mr-1"></i>提取参数';
            }
        }
    };

    if (parseUrlBtn) parseUrlBtn.addEventListener('click', handleParseUrl);

    // URL清空按钮功能
    if (clearUrlBtn) {
        clearUrlBtn.addEventListener('click', () => {
            if (rawUrlInput) rawUrlInput.value = '';
            if (baseUrlOutput) {
                baseUrlOutput.innerHTML = '';
                baseUrlOutput.dataset.plainText = '';
            }
            if (paramsOutput) {
                paramsOutput.innerHTML = '';
                paramsOutput.dataset.plainText = '';
            }
            if (urlMessageArea) urlMessageArea.textContent = '';
            if (urlInputMessageArea) urlInputMessageArea.textContent = '';
            if (urlInputMessageArea) urlInputMessageArea.textContent = '已清空所有内容';
            setTimeout(() => {
                if (urlInputMessageArea) urlInputMessageArea.textContent = '';
            }, 2000);
            console.log('URL工具已清空');
        });
    }

    if (loadExampleUrlBtn) {
        loadExampleUrlBtn.addEventListener('click', () => {
            if (rawUrlInput) rawUrlInput.value = MOCK_URL_DATA;
            if (urlInputMessageArea) urlInputMessageArea.textContent = '已载入示例数据';
            setTimeout(() => {
                if (urlInputMessageArea) urlInputMessageArea.textContent = '';
            }, 2000);
            handleParseUrl();
        });
    }
    if (urlToolContainer) {
        urlToolContainer.addEventListener('click', (e) => {
            const button = e.target.closest('[data-action]');
            if (button) {
                const action = button.dataset.action;
                switch (action) {
                    case 'copy-base-url':
                        window.copyToClipboard('base-url-output');
                        break;
                    case 'copy-params':
                        window.copyToClipboard('params-output');
                        break;
                }
            }
        });
    }

    // --- Base64 编码解码工具逻辑 ---
    const base64EncodeBtn = document.getElementById('base64-encode-btn');
    const base64DecodeBtn = document.getElementById('base64-decode-btn');
    const clearBase64PlainBtn = document.getElementById('clear-base64-plain-btn');
    const clearBase64EncodedBtn = document.getElementById('clear-base64-encoded-btn');
    const copyBase64PlainBtn = document.getElementById('copy-base64-plain-btn');
    const copyBase64EncodedBtn = document.getElementById('copy-base64-encoded-btn');
    const base64PlainInput = document.getElementById('base64-plain-input');
    const base64B64Input = document.getElementById('base64-b64-input');
    const base64MessageArea = document.getElementById('base64-message-area');
    const base64PlainMessageArea = document.getElementById('base64-plain-message-area');
    const base64EncodedMessageArea = document.getElementById('base64-encoded-message-area');
    const base64ToolContainer = document.getElementById('base64-tool');

    async function handleBase64Action(action) {
        let endpoint, payload, inputElement, outputElement;
        if (action === 'encode') {
            endpoint = '/api/v1/base64/encode';
            inputElement = base64PlainInput;
            outputElement = base64B64Input;
            payload = {plain_text: inputElement.value};
        } else {
            endpoint = '/api/v1/base64/decode';
            inputElement = base64B64Input;
            outputElement = base64PlainInput;
            payload = {b64_string: inputElement.value};
        }
        if (!inputElement || !inputElement.value.trim()) return;
        if (base64MessageArea) base64MessageArea.textContent = '';
        const btn = (action === 'encode') ? base64EncodeBtn : base64DecodeBtn;
        if (btn) {
            btn.disabled = true;
            btn.classList.add('opacity-50');
        }
        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(payload)
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);
            if (outputElement) outputElement.value = result.result;
        } catch (error) {
            if (base64MessageArea) base64MessageArea.textContent = `错误: ${error.message}`;
        } finally {
            if (btn) {
                btn.disabled = false;
                btn.classList.remove('opacity-50');
            }
        }
    }

    if (base64EncodeBtn) base64EncodeBtn.addEventListener('click', () => handleBase64Action('encode'));
    if (base64DecodeBtn) base64DecodeBtn.addEventListener('click', () => handleBase64Action('decode'));

    // Base64明文清空按钮功能
    if (clearBase64PlainBtn) {
        clearBase64PlainBtn.addEventListener('click', () => {
            if (base64PlainInput) base64PlainInput.value = '';
            if (base64PlainMessageArea) base64PlainMessageArea.textContent = '已清空明文';
            setTimeout(() => {
                if (base64PlainMessageArea) base64PlainMessageArea.textContent = '';
            }, 2000);
        });
    }

    // Base64编码清空按钮功能
    if (clearBase64EncodedBtn) {
        clearBase64EncodedBtn.addEventListener('click', () => {
            if (base64B64Input) base64B64Input.value = '';
            if (base64EncodedMessageArea) base64EncodedMessageArea.textContent = '已清空Base64';
            setTimeout(() => {
                if (base64EncodedMessageArea) base64EncodedMessageArea.textContent = '';
            }, 2000);
        });
    }

    // Base64明文复制按钮功能
    if (copyBase64PlainBtn) {
        copyBase64PlainBtn.addEventListener('click', () => {
            if (base64PlainInput && base64PlainInput.value) {
                navigator.clipboard.writeText(base64PlainInput.value)
                    .then(() => window.showCopyModal())
                    .catch(err => console.error('复制失败:', err));
            }
        });
    }

    // Base64编码复制按钮功能
    if (copyBase64EncodedBtn) {
        copyBase64EncodedBtn.addEventListener('click', () => {
            if (base64B64Input && base64B64Input.value) {
                navigator.clipboard.writeText(base64B64Input.value)
                    .then(() => window.showCopyModal())
                    .catch(err => console.error('复制失败:', err));
            }
        });
    }

    // Base64载入示例按钮功能
    const loadBase64ExampleBtn = document.getElementById('load-base64-example-btn');
    const loadBase64EncodedExampleBtn = document.getElementById('load-base64-encoded-example-btn');

    if (loadBase64ExampleBtn) {
        loadBase64ExampleBtn.addEventListener('click', () => {
            if (base64PlainInput) {
                base64PlainInput.value = ENCODING_EXAMPLES.base64.original;
                if (base64PlainMessageArea) {
                    base64PlainMessageArea.textContent = '已载入原始示例';
                    base64PlainMessageArea.className = 'mt-2 text-sm text-green-600 h-5 flex-shrink-0';
                    setTimeout(() => {
                        if (base64PlainMessageArea) {
                            base64PlainMessageArea.textContent = '';
                            base64PlainMessageArea.className = 'mt-2 text-sm text-gray-500 h-5 flex-shrink-0';
                        }
                    }, 2000);
                }
            }
        });
    }

    if (loadBase64EncodedExampleBtn) {
        loadBase64EncodedExampleBtn.addEventListener('click', () => {
            if (base64B64Input) {
                base64B64Input.value = ENCODING_EXAMPLES.base64.encoded;
                if (base64EncodedMessageArea) {
                    base64EncodedMessageArea.textContent = '已载入编码示例';
                    base64EncodedMessageArea.className = 'mt-2 text-sm text-green-600 h-5 flex-shrink-0';
                    setTimeout(() => {
                        if (base64EncodedMessageArea) {
                            base64EncodedMessageArea.textContent = '';
                            base64EncodedMessageArea.className = 'mt-2 text-sm text-gray-500 h-5 flex-shrink-0';
                        }
                    }, 2000);
                }
            }
        });
    }

    // 旧的datetime工具已被删除，使用新的分离工具

    // --- 通用函数 ---
    window.showCopyModal = function () {
        if (!copyModal) return;
        copyModal.classList.remove('hidden', 'opacity-0');
        setTimeout(() => {
            copyModal.classList.add('opacity-0');
            setTimeout(() => copyModal.classList.add('hidden'), 300);
        }, 1500);
    };

    window.copyToClipboard = function (elementId) {
        const element = document.getElementById(elementId);
        if (!element) {
            console.error(`复制失败: 未找到元素 #${elementId}`);
            return;
        }
        const textToCopy = element.dataset.plainText;
        if (textToCopy === undefined || textToCopy === null) {
            // 对于textarea或input，如果data-plain-text不存在，则尝试复制其value
            if (element.value !== undefined) {
                navigator.clipboard.writeText(element.value).then(window.showCopyModal).catch(err => {
                    console.error('复制失败: ', err);
                    alert('复制失败!');
                });
            } else {
                console.error(`复制失败: 元素 #${elementId} 没有 "data-plain-text" 属性或 "value"。`);
            }
            return;
        }
        navigator.clipboard.writeText(textToCopy).then(window.showCopyModal).catch(err => {
            console.error('复制失败: ', err);
            alert('复制失败!');
        });
    };

// --- 【全新】通用搜索功能模块 ---

    // 存储每个工具的原始HTML和搜索状态
    const searchState = {
        json: { originalHTML: '', matches: [], currentIndex: -1, term: '' },
        xml: { originalHTML: '', matches: [], currentIndex: -1, term: '' },
        'html-format': { originalHTML: '', matches: [], currentIndex: -1, term: '' },
    };

    /**
     * 切换搜索栏的显示和隐藏
     * @param {string} toolPrefix - 'json', 'xml', or 'html-format'
     */
    function toggleSearchBar(toolPrefix) {
        const toolContainer = document.getElementById(`${toolPrefix}-tool`);
        if (!toolContainer) {
            console.error(`工具容器 ${toolPrefix}-tool 未找到`);
            return;
        }

        const searchBar = toolContainer.querySelector('.search-bar-container');
        const searchInput = toolContainer.querySelector('.search-input');
        // 处理输出元素ID的映射
        const outputId = toolPrefix === 'html-format' ? 'html-output' : `${toolPrefix}-output`;
        const outputEl = document.getElementById(outputId);

        if (!searchBar || !searchInput || !outputEl) {
            console.error(`搜索相关元素未找到: searchBar=${!!searchBar}, searchInput=${!!searchInput}, outputEl=${!!outputEl}`);
            return;
        }

        console.log(`搜索栏当前状态: hidden=${searchBar.classList.contains('hidden')}`);

        if (searchBar.classList.contains('hidden')) {
            // 显示搜索栏
            searchBar.classList.remove('hidden');
            searchBar.classList.add('flex');

            // 存储原始 HTML（只在第一次或内容变化时存储）
            if (!searchState[toolPrefix].originalHTML || searchState[toolPrefix].originalHTML !== outputEl.innerHTML) {
                searchState[toolPrefix].originalHTML = outputEl.innerHTML;
                console.log(`已存储 ${toolPrefix} 的原始HTML，长度: ${searchState[toolPrefix].originalHTML.length}`);
            }

            // 确保事件监听器只绑定一次
            if (!searchBar.dataset.initialized) {
                initializeSearchBarEvents(toolPrefix, toolContainer);
                searchBar.dataset.initialized = 'true';
                console.log(`已初始化 ${toolPrefix} 搜索栏事件`);
            }

            // 延迟设置焦点，确保元素已显示
            setTimeout(() => {
                searchInput.focus();
                console.log(`已设置 ${toolPrefix} 搜索框焦点`);
            }, 100);

        } else {
            // 隐藏搜索栏
            searchBar.classList.add('hidden');
            searchBar.classList.remove('flex');

            // 恢复原始 HTML
            if (searchState[toolPrefix].originalHTML) {
                outputEl.innerHTML = searchState[toolPrefix].originalHTML;
                console.log(`已恢复 ${toolPrefix} 的原始HTML`);
            }

            // 清理搜索状态
            searchState[toolPrefix].matches = [];
            searchState[toolPrefix].currentIndex = -1;
            searchState[toolPrefix].term = '';
            searchInput.value = '';

            // 更新搜索UI
            updateSearchUI(toolPrefix);
        }
    }

    /**
     * 为搜索栏绑定事件
     */
    function initializeSearchBarEvents(toolPrefix, container) {
        const searchInput = container.querySelector('.search-input');
        const prevBtn = container.querySelector('.search-prev-btn');
        const nextBtn = container.querySelector('.search-next-btn');
        const closeBtn = container.querySelector('.search-close-btn');

        searchInput.addEventListener('input', () => {
            performSearch(toolPrefix, searchInput.value);
        });

        prevBtn.addEventListener('click', () => navigateToMatch(toolPrefix, 'prev'));
        nextBtn.addEventListener('click', () => navigateToMatch(toolPrefix, 'next'));
        closeBtn.addEventListener('click', () => toggleSearchBar(toolPrefix));
    }

    /**
     * 执行搜索操作
     */
    function performSearch(toolPrefix, term) {
        const state = searchState[toolPrefix];
        state.term = term;

        // 处理输出元素ID的映射
        const outputId = toolPrefix === 'html-format' ? 'html-output' : `${toolPrefix}-output`;
        const outputEl = document.getElementById(outputId);

        // 确保有原始HTML可以恢复
        if (!state.originalHTML) {
            console.warn(`${toolPrefix} 没有原始HTML，无法执行搜索`);
            return;
        }

        // 恢复到干净状态再搜索
        outputEl.innerHTML = state.originalHTML;
        console.log(`执行搜索: "${term}", 原始HTML长度: ${state.originalHTML.length}`);

        if (!term.trim()) {
            state.matches = [];
            state.currentIndex = -1;
            updateSearchUI(toolPrefix);
            console.log(`搜索词为空，已清理搜索状态`);
            return;
        }

        // 执行搜索和高亮
        state.matches = highlightTextInNode(outputEl, term);
        state.currentIndex = state.matches.length > 0 ? 0 : -1;

        console.log(`搜索完成: 找到 ${state.matches.length} 个匹配项`);

        updateSearchUI(toolPrefix);
        if (state.matches.length > 0) {
            navigateToMatch(toolPrefix, 'first');
        }
    }

    /**
     * 在节点中高亮文本并返回匹配的元素数组
     */
    function highlightTextInNode(node, searchTerm) {
        if (!node || !searchTerm) {
            console.log('搜索参数无效:', { node: !!node, searchTerm });
            return [];
        }

        const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
        const walker = document.createTreeWalker(node, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        const matches = [];
        let processedNodes = 0;

        // 收集所有文本节点，避免在遍历时修改DOM
        const textNodes = [];
        while (textNode = walker.nextNode()) {
            if (textNode.parentElement.tagName === 'STYLE' || textNode.parentElement.tagName === 'SCRIPT') continue;
            textNodes.push(textNode);
        }

        console.log(`找到 ${textNodes.length} 个文本节点待处理`);

        // 处理每个文本节点
        textNodes.forEach(textNode => {
            const text = textNode.textContent;
            if (!text || !regex.test(text)) return;

            // 重置正则表达式的lastIndex
            regex.lastIndex = 0;

            let match;
            let lastIndex = 0;
            const fragment = document.createDocumentFragment();
            let nodeMatches = 0;

            while ((match = regex.exec(text)) !== null) {
                // 添加匹配前的文本
                if (match.index > lastIndex) {
                    fragment.appendChild(document.createTextNode(text.substring(lastIndex, match.index)));
                }

                // 创建并添加高亮元素
                const highlightSpan = document.createElement('span');
                highlightSpan.className = 'search-highlight';
                highlightSpan.textContent = match[0];
                fragment.appendChild(highlightSpan);
                matches.push(highlightSpan);
                nodeMatches++;

                lastIndex = regex.lastIndex;
            }

            // 添加匹配后的剩余文本
            if (lastIndex < text.length) {
                fragment.appendChild(document.createTextNode(text.substring(lastIndex)));
            }

            // 替换原文本节点
            if (fragment.childNodes.length > 0) {
                textNode.parentNode.replaceChild(fragment, textNode);
                processedNodes++;
            }

            console.log(`节点处理完成，匹配数: ${nodeMatches}`);
        });

        console.log(`高亮处理完成: 处理了 ${processedNodes} 个节点，总匹配数: ${matches.length}`);
        return matches;
    }

    /**
     * 更新搜索UI（计数器和按钮状态）
     */
    function updateSearchUI(toolPrefix) {
        const container = document.getElementById(`${toolPrefix}-tool`);
        const state = searchState[toolPrefix];

        const countSpan = container.querySelector('.search-count');
        const prevBtn = container.querySelector('.search-prev-btn');
        const nextBtn = container.querySelector('.search-next-btn');

        const total = state.matches.length;
        const current = state.currentIndex + 1;

        countSpan.textContent = `${current} / ${total}`;

        prevBtn.disabled = total === 0 || current <= 1;
        nextBtn.disabled = total === 0 || current === total;
    }

    /**
     * 导航到指定的匹配项
     */
    function navigateToMatch(toolPrefix, direction) {
        const state = searchState[toolPrefix];
        if (state.matches.length === 0) return;

        // 移除旧的当前匹配样式
        if (state.currentIndex !== -1 && state.matches[state.currentIndex]) {
            state.matches[state.currentIndex].classList.remove('current-match');
        }

        // 计算新索引
        if (direction === 'next') {
            state.currentIndex = (state.currentIndex + 1) % state.matches.length;
        } else if (direction === 'prev') {
            state.currentIndex = (state.currentIndex - 1 + state.matches.length) % state.matches.length;
        } else if (direction === 'first') {
            state.currentIndex = 0;
        }

        // 添加新的当前匹配样式并滚动
        const currentMatchEl = state.matches[state.currentIndex];
        if (currentMatchEl) {
            currentMatchEl.classList.add('current-match');
            currentMatchEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        updateSearchUI(toolPrefix);
    }
    const mainElement = document.querySelector('main');
    if (mainElement) {
        mainElement.addEventListener('click', (e) => {
            const toggler = e.target.closest('.toggler');
            if (toggler) {
                const icon = toggler.querySelector('i');
                const content = toggler.nextElementSibling;
                if (content && icon) {
                    const isHidden = content.style.display === 'none';
                    content.style.display = isHidden ? 'block' : 'none';
                    icon.classList.toggle('fa-plus-square', !isHidden);
                    icon.classList.toggle('fa-minus-square', isHidden);
                }
            }
        });
    }

    // --- 【现代化】文本对比工具逻辑 ---
    const compareTextsBtn = document.getElementById('compare-texts-btn');
    const compareAToBBtn = document.getElementById('compare-a-to-b-btn');
    const compareBToABtn = document.getElementById('compare-b-to-a-btn');
    const clearAllTextsBtn = document.getElementById('clear-all-texts-btn');
    const textAInput = document.getElementById('text-a-input');
    const textBInput = document.getElementById('text-b-input');
    const textALineNumbers = document.getElementById('text-a-line-numbers');
    const textBLineNumbers = document.getElementById('text-b-line-numbers');
    const textAStats = document.getElementById('text-a-stats');
    const textBStats = document.getElementById('text-b-stats');
    const diffResult = document.getElementById('diff-result');
    const diffStats = document.getElementById('diff-stats');
    const diffSidebar = document.getElementById('diff-sidebar');
    const diffNavigation = document.getElementById('diff-navigation');
    const textCompareMessageArea = document.getElementById('text-compare-message-area');

    const loadExampleTextABtn = document.getElementById('load-example-text-a');
    const loadExampleTextBBtn = document.getElementById('load-example-text-b');
    const clearTextABtn = document.getElementById('clear-text-a');
    const clearTextBBtn = document.getElementById('clear-text-b');
    const copyTextABtn = document.getElementById('copy-text-a');
    const copyTextBBtn = document.getElementById('copy-text-b');

    const ignoreWhitespaceCheckbox = document.getElementById('ignore-whitespace');
    const ignoreCaseCheckbox = document.getElementById('ignore-case');
    const ignoreEmptyLinesCheckbox = document.getElementById('ignore-empty-lines');
    const showLineNumbersCheckbox = document.getElementById('show-line-numbers');
    const realTimeCompareCheckbox = document.getElementById('real-time-compare');

    // 新增的现代化功能元素
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    const helpBtn = document.getElementById('help-btn');
    const helpModal = document.getElementById('help-modal');
    const closeHelpModal = document.getElementById('close-help-modal');
    const searchInput = document.getElementById('search-input');
    const searchPrevBtn = document.getElementById('search-prev');
    const searchNextBtn = document.getElementById('search-next');
    const undoBtn = document.getElementById('undo-btn');
    const redoBtn = document.getElementById('redo-btn');
    const resizer = document.getElementById('resizer');

    // 状态管理
    let isDarkTheme = false;
    let isFullscreen = false;
    let realTimeTimeout = null;
    let currentCompareMode = 'both'; // 'both', 'a-to-b', 'b-to-a'
    let searchMatches = [];
    let currentSearchIndex = -1;
    let undoStack = [];
    let redoStack = [];
    let isResizing = false;

    // 初始化函数
    function initializeTextCompare() {
        updateLineNumbers();
        updateTextStats();
        setupEventListeners();
        setupKeyboardShortcuts();
        setupResizer();
    }

    // 更新行号显示
    function updateLineNumbers() {
        if (!textALineNumbers || !textBLineNumbers) return;

        const textALines = textAInput.value.split('\n');
        const textBLines = textBInput.value.split('\n');

        const maxLinesA = Math.max(textALines.length, 1);
        const maxLinesB = Math.max(textBLines.length, 1);

        // 生成行号
        const lineNumbersA = Array.from({length: maxLinesA}, (_, i) => i + 1).join('\n');
        const lineNumbersB = Array.from({length: maxLinesB}, (_, i) => i + 1).join('\n');

        textALineNumbers.textContent = lineNumbersA;
        textBLineNumbers.textContent = lineNumbersB;
    }

    // 更新文本统计
    function updateTextStats() {
        if (!textAStats || !textBStats) return;

        const textA = textAInput.value;
        const textB = textBInput.value;

        const statsA = {
            chars: textA.length,
            lines: textA.split('\n').length,
            words: textA.trim() ? textA.trim().split(/\s+/).length : 0
        };

        const statsB = {
            chars: textB.length,
            lines: textB.split('\n').length,
            words: textB.trim() ? textB.trim().split(/\s+/).length : 0
        };

        textAStats.textContent = `${statsA.chars} 字符, ${statsA.lines} 行, ${statsA.words} 词`;
        textBStats.textContent = `${statsB.chars} 字符, ${statsB.lines} 行, ${statsB.words} 词`;
    }

    // 主题切换功能
    function toggleTheme() {
        isDarkTheme = !isDarkTheme;
        const textCompareTool = document.getElementById('text-compare-tool');
        const themeIcon = themeToggleBtn.querySelector('i');

        if (isDarkTheme) {
            textCompareTool.classList.add('text-compare-dark');
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
        } else {
            textCompareTool.classList.remove('text-compare-dark');
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
        }

        // 保存主题偏好
        localStorage.setItem('textCompareTheme', isDarkTheme ? 'dark' : 'light');
    }

    // 全屏模式切换
    function toggleFullscreen() {
        const textCompareTool = document.getElementById('text-compare-tool');
        const fullscreenIcon = fullscreenBtn.querySelector('i');

        isFullscreen = !isFullscreen;

        if (isFullscreen) {
            textCompareTool.classList.add('fullscreen-mode');
            fullscreenIcon.classList.remove('fa-expand');
            fullscreenIcon.classList.add('fa-compress');
            document.body.style.overflow = 'hidden';
        } else {
            textCompareTool.classList.remove('fullscreen-mode');
            fullscreenIcon.classList.remove('fa-compress');
            fullscreenIcon.classList.add('fa-expand');
            document.body.style.overflow = '';
        }
    }

    // 文本对比核心算法 - 增强版
    function compareTexts(textA, textB, options = {}) {
        const {
            ignoreWhitespace = false,
            ignoreCase = false,
            ignoreEmptyLines = false,
            mode = 'both'
        } = options;

        // 预处理文本
        let processedA = textA;
        let processedB = textB;

        if (ignoreCase) {
            processedA = processedA.toLowerCase();
            processedB = processedB.toLowerCase();
        }

        if (ignoreWhitespace) {
            processedA = processedA.replace(/\s+/g, ' ').trim();
            processedB = processedB.replace(/\s+/g, ' ').trim();
        }

        let linesA = processedA.split('\n');
        let linesB = processedB.split('\n');
        let originalA = textA.split('\n');
        let originalB = textB.split('\n');

        if (ignoreEmptyLines) {
            const filterEmpty = (lines, original) => {
                const filtered = [];
                const originalFiltered = [];
                lines.forEach((line, index) => {
                    if (line.trim() !== '') {
                        filtered.push(line);
                        originalFiltered.push(original[index]);
                    }
                });
                return { filtered, originalFiltered };
            };

            const resultA = filterEmpty(linesA, originalA);
            const resultB = filterEmpty(linesB, originalB);
            linesA = resultA.filtered;
            originalA = resultA.originalFiltered;
            linesB = resultB.filtered;
            originalB = resultB.originalFiltered;
        }

        return computeDiff(linesA, linesB, originalA, originalB, mode);
    }

    // 增强的差异计算算法
    function computeDiff(linesA, linesB, originalA, originalB, mode = 'both') {
        const result = [];
        let i = 0, j = 0;
        let addedCount = 0, deletedCount = 0, modifiedCount = 0;

        // 根据模式调整显示逻辑
        const showDeleted = mode === 'both' || mode === 'a-to-b';
        const showAdded = mode === 'both' || mode === 'b-to-a';

        while (i < linesA.length || j < linesB.length) {
            if (i >= linesA.length) {
                // B中剩余的行都是新增的
                if (showAdded) {
                    result.push({ type: 'added', lineA: null, lineB: originalB[j], numA: null, numB: j + 1 });
                    addedCount++;
                }
                j++;
            } else if (j >= linesB.length) {
                // A中剩余的行都是删除的
                if (showDeleted) {
                    result.push({ type: 'deleted', lineA: originalA[i], lineB: null, numA: i + 1, numB: null });
                    deletedCount++;
                }
                i++;
            } else if (linesA[i] === linesB[j]) {
                // 相同的行
                result.push({ type: 'equal', lineA: originalA[i], lineB: originalB[j], numA: i + 1, numB: j + 1 });
                i++;
                j++;
            } else {
                // 查找最佳匹配
                let foundMatch = false;

                // 向前查找B中是否有匹配A[i]的行
                for (let k = j + 1; k < Math.min(j + 5, linesB.length); k++) {
                    if (linesA[i] === linesB[k]) {
                        // 在B中找到了匹配，说明j到k-1的行是新增的
                        for (let l = j; l < k; l++) {
                            if (showAdded) {
                                result.push({ type: 'added', lineA: null, lineB: originalB[l], numA: null, numB: l + 1 });
                                addedCount++;
                            }
                        }
                        result.push({ type: 'equal', lineA: originalA[i], lineB: originalB[k], numA: i + 1, numB: k + 1 });
                        i++;
                        j = k + 1;
                        foundMatch = true;
                        break;
                    }
                }

                if (!foundMatch) {
                    // 向前查找A中是否有匹配B[j]的行
                    for (let k = i + 1; k < Math.min(i + 5, linesA.length); k++) {
                        if (linesA[k] === linesB[j]) {
                            // 在A中找到了匹配，说明i到k-1的行是删除的
                            for (let l = i; l < k; l++) {
                                if (showDeleted) {
                                    result.push({ type: 'deleted', lineA: originalA[l], lineB: null, numA: l + 1, numB: null });
                                    deletedCount++;
                                }
                            }
                            result.push({ type: 'equal', lineA: originalA[k], lineB: originalB[j], numA: k + 1, numB: j + 1 });
                            i = k + 1;
                            j++;
                            foundMatch = true;
                            break;
                        }
                    }
                }

                if (!foundMatch) {
                    // 没找到匹配，认为是修改
                    if (mode === 'both') {
                        result.push({ type: 'modified', lineA: originalA[i], lineB: originalB[j], numA: i + 1, numB: j + 1 });
                        modifiedCount++;
                    } else if (mode === 'a-to-b') {
                        result.push({ type: 'deleted', lineA: originalA[i], lineB: null, numA: i + 1, numB: null });
                        result.push({ type: 'added', lineA: null, lineB: originalB[j], numA: null, numB: j + 1 });
                        deletedCount++;
                        addedCount++;
                    } else if (mode === 'b-to-a') {
                        result.push({ type: 'added', lineA: null, lineB: originalB[j], numA: null, numB: j + 1 });
                        result.push({ type: 'deleted', lineA: originalA[i], lineB: null, numA: i + 1, numB: null });
                        addedCount++;
                        deletedCount++;
                    }
                    i++;
                    j++;
                }
            }
        }

        return {
            diffs: result,
            stats: { added: addedCount, deleted: deletedCount, modified: modifiedCount }
        };
    }

    // 简化的差异计算算法
    function computeDiff(linesA, linesB, originalA, originalB) {
        const result = [];
        let i = 0, j = 0;
        let addedCount = 0, deletedCount = 0, modifiedCount = 0;

        while (i < linesA.length || j < linesB.length) {
            if (i >= linesA.length) {
                // B中剩余的行都是新增的
                result.push({ type: 'added', lineA: null, lineB: originalB[j], numA: null, numB: j + 1 });
                addedCount++;
                j++;
            } else if (j >= linesB.length) {
                // A中剩余的行都是删除的
                result.push({ type: 'deleted', lineA: originalA[i], lineB: null, numA: i + 1, numB: null });
                deletedCount++;
                i++;
            } else if (linesA[i] === linesB[j]) {
                // 相同的行
                result.push({ type: 'equal', lineA: originalA[i], lineB: originalB[j], numA: i + 1, numB: j + 1 });
                i++;
                j++;
            } else {
                // 查找最佳匹配
                let foundMatch = false;

                // 向前查找B中是否有匹配A[i]的行
                for (let k = j + 1; k < Math.min(j + 5, linesB.length); k++) {
                    if (linesA[i] === linesB[k]) {
                        // 在B中找到了匹配，说明j到k-1的行是新增的
                        for (let l = j; l < k; l++) {
                            result.push({ type: 'added', lineA: null, lineB: originalB[l], numA: null, numB: l + 1 });
                            addedCount++;
                        }
                        result.push({ type: 'equal', lineA: originalA[i], lineB: originalB[k], numA: i + 1, numB: k + 1 });
                        i++;
                        j = k + 1;
                        foundMatch = true;
                        break;
                    }
                }

                if (!foundMatch) {
                    // 向前查找A中是否有匹配B[j]的行
                    for (let k = i + 1; k < Math.min(i + 5, linesA.length); k++) {
                        if (linesA[k] === linesB[j]) {
                            // 在A中找到了匹配，说明i到k-1的行是删除的
                            for (let l = i; l < k; l++) {
                                result.push({ type: 'deleted', lineA: originalA[l], lineB: null, numA: l + 1, numB: null });
                                deletedCount++;
                            }
                            result.push({ type: 'equal', lineA: originalA[k], lineB: originalB[j], numA: k + 1, numB: j + 1 });
                            i = k + 1;
                            j++;
                            foundMatch = true;
                            break;
                        }
                    }
                }

                if (!foundMatch) {
                    // 没找到匹配，认为是修改
                    result.push({ type: 'modified', lineA: originalA[i], lineB: originalB[j], numA: i + 1, numB: j + 1 });
                    modifiedCount++;
                    i++;
                    j++;
                }
            }
        }

        return {
            diffs: result,
            stats: { added: addedCount, deleted: deletedCount, modified: modifiedCount }
        };
    }

    // 增强的差异结果渲染
    function renderDiffResult(diffData, showLineNumbers) {
        const { diffs, stats } = diffData;
        let html = '';
        let navigationItems = [];

        diffs.forEach((diff, index) => {
            const lineNumA = showLineNumbers && diff.numA ? `<span class="line-number text-gray-400 mr-2">${diff.numA.toString().padStart(3, ' ')}</span>` : '';
            const lineNumB = showLineNumbers && diff.numB ? `<span class="line-number text-gray-400 mr-2">${diff.numB.toString().padStart(3, ' ')}</span>` : '';

            switch (diff.type) {
                case 'equal':
                    html += `<div class="diff-line unchanged" data-line="${index}">${lineNumA}${escapeHtml(diff.lineA || '')}</div>`;
                    break;
                case 'deleted':
                    html += `<div class="diff-line removed" data-line="${index}" data-type="removed">- ${lineNumA}${escapeHtml(diff.lineA || '')}</div>`;
                    navigationItems.push({ type: 'removed', index, line: diff.numA });
                    break;
                case 'added':
                    html += `<div class="diff-line added" data-line="${index}" data-type="added">+ ${lineNumB}${escapeHtml(diff.lineB || '')}</div>`;
                    navigationItems.push({ type: 'added', index, line: diff.numB });
                    break;
                case 'modified':
                    html += `<div class="diff-line removed" data-line="${index}" data-type="modified">- ${lineNumA}${escapeHtml(diff.lineA || '')}</div>`;
                    html += `<div class="diff-line added" data-line="${index + 0.5}" data-type="modified">+ ${lineNumB}${escapeHtml(diff.lineB || '')}</div>`;
                    navigationItems.push({ type: 'modified', index, line: diff.numA });
                    break;
            }
        });

        // 更新导航侧边栏
        updateDiffNavigation(navigationItems);

        return { html, stats };
    }

    // 更新差异导航
    function updateDiffNavigation(navigationItems) {
        if (!diffNavigation) return;

        diffNavigation.innerHTML = '';
        navigationItems.forEach((item, index) => {
            const navItem = document.createElement('div');
            navItem.className = `diff-nav-item ${item.type}`;
            navItem.title = `${item.type} - 行 ${item.line}`;
            navItem.addEventListener('click', () => {
                const targetElement = document.querySelector(`[data-line="${item.index}"]`);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    targetElement.classList.add('animate-slide-in');
                    setTimeout(() => targetElement.classList.remove('animate-slide-in'), 300);
                }
            });
            diffNavigation.appendChild(navItem);
        });
    }

    // 搜索功能
    function performDiffSearch(searchTerm) {
        if (!searchTerm.trim()) {
            clearSearchHighlights();
            return;
        }

        const diffLines = diffResult.querySelectorAll('.diff-line');
        searchMatches = [];
        currentSearchIndex = -1;

        // 清除之前的高亮
        clearSearchHighlights();

        // 搜索并高亮
        diffLines.forEach((line, index) => {
            const text = line.textContent;
            const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
            let match;

            while ((match = regex.exec(text)) !== null) {
                searchMatches.push({ element: line, index: searchMatches.length });

                // 高亮匹配的文本
                const highlightedText = text.replace(regex, (matched) =>
                    `<span class="search-highlight">${matched}</span>`
                );
                line.innerHTML = highlightedText;
            }
        });

        // 跳转到第一个匹配项
        if (searchMatches.length > 0) {
            navigateToSearchMatch(0);
        }

        updateSearchStats();
    }

    // 清除搜索高亮
    function clearSearchHighlights() {
        const highlights = diffResult.querySelectorAll('.search-highlight');
        highlights.forEach(highlight => {
            const parent = highlight.parentNode;
            parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
            parent.normalize();
        });
    }

    // 导航到搜索匹配项
    function navigateToSearchMatch(index) {
        if (searchMatches.length === 0) return;

        // 移除当前高亮
        if (currentSearchIndex >= 0 && searchMatches[currentSearchIndex]) {
            const currentHighlight = searchMatches[currentSearchIndex].element.querySelector('.search-highlight.current');
            if (currentHighlight) {
                currentHighlight.classList.remove('current');
            }
        }

        currentSearchIndex = index;
        const match = searchMatches[currentSearchIndex];

        if (match) {
            // 添加当前高亮
            const highlight = match.element.querySelector('.search-highlight');
            if (highlight) {
                highlight.classList.add('current');
            }

            // 滚动到视图
            match.element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        updateSearchStats();
    }

    // 更新搜索统计
    function updateSearchStats() {
        const total = searchMatches.length;
        const current = currentSearchIndex + 1;

        // 这里可以更新搜索统计显示
        console.log(`搜索结果: ${current}/${total}`);
    }

    // HTML转义函数
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 执行文本对比 - 增强版
    function performTextComparison(mode = 'both') {
        const textA = textAInput.value;
        const textB = textBInput.value;

        if (!textA.trim() && !textB.trim()) {
            diffResult.innerHTML = `
                <div class="text-center text-gray-400 py-8">
                    <i class="fas fa-code-branch text-4xl mb-4"></i>
                    <p>请在上方输入两段文本，然后点击"开始对比"按钮</p>
                    <p class="text-xs mt-2">支持实时对比、双向分析、智能差异检测</p>
                </div>`;
            diffStats.textContent = '等待对比...';
            return;
        }

        currentCompareMode = mode;

        const options = {
            ignoreWhitespace: ignoreWhitespaceCheckbox?.checked || false,
            ignoreCase: ignoreCaseCheckbox?.checked || false,
            ignoreEmptyLines: ignoreEmptyLinesCheckbox?.checked || false,
            mode: mode
        };

        const showLineNumbers = showLineNumbersCheckbox?.checked || false;

        try {
            const diffData = compareTexts(textA, textB, options);
            const { html, stats } = renderDiffResult(diffData, showLineNumbers);

            diffResult.innerHTML = html || '<div class="text-center text-gray-400 py-8">文本完全相同</div>';

            const totalChanges = stats.added + stats.deleted + stats.modified;
            if (totalChanges === 0) {
                diffStats.textContent = '文本完全相同';
            } else {
                let modeText = '';
                switch (mode) {
                    case 'a-to-b':
                        modeText = ' (A→B)';
                        break;
                    case 'b-to-a':
                        modeText = ' (B→A)';
                        break;
                    default:
                        modeText = '';
                }
                diffStats.textContent = `差异${modeText}: +${stats.added} -${stats.deleted} ~${stats.modified}`;
            }

            if (textCompareMessageArea) textCompareMessageArea.textContent = '';
        } catch (error) {
            if (textCompareMessageArea) textCompareMessageArea.textContent = `对比失败: ${error.message}`;
            console.error('文本对比错误:', error);
        }
    }

    // 实时对比功能
    function setupRealTimeCompare() {
        const handleInput = () => {
            if (!realTimeCompareCheckbox?.checked) return;

            clearTimeout(realTimeTimeout);
            realTimeTimeout = setTimeout(() => {
                performTextComparison(currentCompareMode);
            }, 500); // 500ms 防抖
        };

        if (textAInput) textAInput.addEventListener('input', handleInput);
        if (textBInput) textBInput.addEventListener('input', handleInput);
    }

    // 撤销重做功能
    function saveState() {
        const state = {
            textA: textAInput.value,
            textB: textBInput.value,
            timestamp: Date.now()
        };

        undoStack.push(state);
        if (undoStack.length > 50) { // 限制历史记录数量
            undoStack.shift();
        }

        redoStack = []; // 清空重做栈
        updateUndoRedoButtons();
    }

    function undo() {
        if (undoStack.length === 0) return;

        const currentState = {
            textA: textAInput.value,
            textB: textBInput.value,
            timestamp: Date.now()
        };

        redoStack.push(currentState);
        const previousState = undoStack.pop();

        textAInput.value = previousState.textA;
        textBInput.value = previousState.textB;

        updateLineNumbers();
        updateTextStats();
        updateUndoRedoButtons();

        if (realTimeCompareCheckbox?.checked) {
            performTextComparison(currentCompareMode);
        }
    }

    function redo() {
        if (redoStack.length === 0) return;

        const currentState = {
            textA: textAInput.value,
            textB: textBInput.value,
            timestamp: Date.now()
        };

        undoStack.push(currentState);
        const nextState = redoStack.pop();

        textAInput.value = nextState.textA;
        textBInput.value = nextState.textB;

        updateLineNumbers();
        updateTextStats();
        updateUndoRedoButtons();

        if (realTimeCompareCheckbox?.checked) {
            performTextComparison(currentCompareMode);
        }
    }

    function updateUndoRedoButtons() {
        if (undoBtn) undoBtn.disabled = undoStack.length === 0;
        if (redoBtn) redoBtn.disabled = redoStack.length === 0;
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 主要对比按钮
        if (compareTextsBtn) {
            compareTextsBtn.addEventListener('click', () => performTextComparison('both'));
        }

        if (compareAToBBtn) {
            compareAToBBtn.addEventListener('click', () => performTextComparison('a-to-b'));
        }

        if (compareBToABtn) {
            compareBToABtn.addEventListener('click', () => performTextComparison('b-to-a'));
        }

        if (clearAllTextsBtn) {
            clearAllTextsBtn.addEventListener('click', () => {
                saveState(); // 保存状态用于撤销
                textAInput.value = '';
                textBInput.value = '';
                diffResult.innerHTML = `
                    <div class="text-center text-gray-400 py-8">
                        <i class="fas fa-code-branch text-4xl mb-4"></i>
                        <p>请在上方输入两段文本，然后点击"开始对比"按钮</p>
                        <p class="text-xs mt-2">支持实时对比、双向分析、智能差异检测</p>
                    </div>`;
                diffStats.textContent = '等待对比...';
                updateLineNumbers();
                updateTextStats();
                if (textCompareMessageArea) textCompareMessageArea.textContent = '';
            });
        }

        // 示例数据加载
        if (loadExampleTextABtn) {
            loadExampleTextABtn.addEventListener('click', () => {
                saveState();
                textAInput.value = MOCK_TEXT_A;
                updateLineNumbers();
                updateTextStats();
                if (realTimeCompareCheckbox?.checked) {
                    performTextComparison(currentCompareMode);
                }
            });
        }

        if (loadExampleTextBBtn) {
            loadExampleTextBBtn.addEventListener('click', () => {
                saveState();
                textBInput.value = MOCK_TEXT_B;
                updateLineNumbers();
                updateTextStats();
                if (realTimeCompareCheckbox?.checked) {
                    performTextComparison(currentCompareMode);
                }
            });
        }

        // 清空单个文本
        if (clearTextABtn) {
            clearTextABtn.addEventListener('click', () => {
                saveState();
                textAInput.value = '';
                updateLineNumbers();
                updateTextStats();
            });
        }

        if (clearTextBBtn) {
            clearTextBBtn.addEventListener('click', () => {
                saveState();
                textBInput.value = '';
                updateLineNumbers();
                updateTextStats();
            });
        }

        // 复制功能
        if (copyTextABtn) {
            copyTextABtn.addEventListener('click', () => {
                if (textAInput.value) {
                    navigator.clipboard.writeText(textAInput.value)
                        .then(() => window.showCopyModal())
                        .catch(err => console.error('复制失败:', err));
                }
            });
        }

        if (copyTextBBtn) {
            copyTextBBtn.addEventListener('click', () => {
                if (textBInput.value) {
                    navigator.clipboard.writeText(textBInput.value)
                        .then(() => window.showCopyModal())
                        .catch(err => console.error('复制失败:', err));
                }
            });
        }

        // 主题切换
        if (themeToggleBtn) {
            themeToggleBtn.addEventListener('click', toggleTheme);
        }

        // 全屏模式
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', toggleFullscreen);
        }

        // 帮助模态框
        if (helpBtn) {
            helpBtn.addEventListener('click', () => {
                helpModal.classList.remove('hidden');
            });
        }

        if (closeHelpModal) {
            closeHelpModal.addEventListener('click', () => {
                helpModal.classList.add('hidden');
            });
        }

        // 点击模态框外部关闭
        if (helpModal) {
            helpModal.addEventListener('click', (e) => {
                if (e.target === helpModal) {
                    helpModal.classList.add('hidden');
                }
            });
        }

        // 撤销重做
        if (undoBtn) {
            undoBtn.addEventListener('click', undo);
        }

        if (redoBtn) {
            redoBtn.addEventListener('click', redo);
        }

        // 搜索功能
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                performDiffSearch(e.target.value);
            });
        }

        if (searchPrevBtn) {
            searchPrevBtn.addEventListener('click', () => {
                if (searchMatches.length > 0) {
                    const newIndex = currentSearchIndex > 0 ? currentSearchIndex - 1 : searchMatches.length - 1;
                    navigateToSearchMatch(newIndex);
                }
            });
        }

        if (searchNextBtn) {
            searchNextBtn.addEventListener('click', () => {
                if (searchMatches.length > 0) {
                    const newIndex = currentSearchIndex < searchMatches.length - 1 ? currentSearchIndex + 1 : 0;
                    navigateToSearchMatch(newIndex);
                }
            });
        }

        // 文本输入监听
        if (textAInput) {
            textAInput.addEventListener('input', () => {
                updateLineNumbers();
                updateTextStats();
            });

            textAInput.addEventListener('scroll', () => {
                if (textALineNumbers) {
                    textALineNumbers.scrollTop = textAInput.scrollTop;
                }
            });
        }

        if (textBInput) {
            textBInput.addEventListener('input', () => {
                updateLineNumbers();
                updateTextStats();
            });

            textBInput.addEventListener('scroll', () => {
                if (textBLineNumbers) {
                    textBLineNumbers.scrollTop = textBInput.scrollTop;
                }
            });
        }

        // 选项变化时自动重新对比
        [ignoreWhitespaceCheckbox, ignoreCaseCheckbox, ignoreEmptyLinesCheckbox, showLineNumbersCheckbox].forEach(checkbox => {
            if (checkbox) {
                checkbox.addEventListener('change', () => {
                    if (textAInput.value.trim() || textBInput.value.trim()) {
                        performTextComparison(currentCompareMode);
                    }
                });
            }
        });

        // 实时对比开关
        if (realTimeCompareCheckbox) {
            realTimeCompareCheckbox.addEventListener('change', () => {
                if (realTimeCompareCheckbox.checked && (textAInput.value.trim() || textBInput.value.trim())) {
                    performTextComparison(currentCompareMode);
                }
            });
        }

        // 设置实时对比
        setupRealTimeCompare();
    }

    // 设置键盘快捷键
    function setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // 只在文本对比工具激活时响应快捷键
            const textCompareTool = document.getElementById('text-compare-tool');
            if (!textCompareTool || textCompareTool.classList.contains('hidden')) return;

            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'Enter':
                        e.preventDefault();
                        performTextComparison('both');
                        break;
                    case 'd':
                    case 'D':
                        e.preventDefault();
                        clearAllTextsBtn?.click();
                        break;
                    case 'z':
                    case 'Z':
                        if (!e.shiftKey) {
                            e.preventDefault();
                            undo();
                        }
                        break;
                    case 'y':
                    case 'Y':
                        e.preventDefault();
                        redo();
                        break;
                    case 'f':
                    case 'F':
                        e.preventDefault();
                        searchInput?.focus();
                        break;
                }
            } else if (e.key === 'F11') {
                e.preventDefault();
                toggleFullscreen();
            } else if (e.key === 'Escape') {
                if (isFullscreen) {
                    toggleFullscreen();
                } else if (helpModal && !helpModal.classList.contains('hidden')) {
                    helpModal.classList.add('hidden');
                }
            }
        });
    }

    // 设置拖拽调整功能
    function setupResizer() {
        if (!resizer) return;

        let startX = 0;
        let startLeftWidth = 0;
        let startRightWidth = 0;

        resizer.addEventListener('mousedown', (e) => {
            isResizing = true;
            startX = e.clientX;

            const leftPanel = resizer.previousElementSibling;
            const rightPanel = resizer.nextElementSibling;

            if (leftPanel && rightPanel) {
                startLeftWidth = leftPanel.offsetWidth;
                startRightWidth = rightPanel.offsetWidth;
            }

            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);

            // 防止文本选择
            document.body.style.userSelect = 'none';
            resizer.style.cursor = 'col-resize';
        });

        function handleMouseMove(e) {
            if (!isResizing) return;

            const deltaX = e.clientX - startX;
            const container = resizer.parentElement;
            const containerWidth = container.offsetWidth;

            const leftPanel = resizer.previousElementSibling;
            const rightPanel = resizer.nextElementSibling;

            if (leftPanel && rightPanel) {
                const newLeftWidth = startLeftWidth + deltaX;
                const newRightWidth = startRightWidth - deltaX;

                // 限制最小宽度
                const minWidth = 200;
                if (newLeftWidth >= minWidth && newRightWidth >= minWidth) {
                    const leftPercent = (newLeftWidth / containerWidth) * 100;
                    const rightPercent = (newRightWidth / containerWidth) * 100;

                    leftPanel.style.width = `${leftPercent}%`;
                    rightPanel.style.width = `${rightPercent}%`;
                }
            }
        }

        function handleMouseUp() {
            isResizing = false;
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);

            document.body.style.userSelect = '';
            resizer.style.cursor = '';
        }

        // 双击重置
        resizer.addEventListener('dblclick', () => {
            const leftPanel = resizer.previousElementSibling;
            const rightPanel = resizer.nextElementSibling;

            if (leftPanel && rightPanel) {
                leftPanel.style.width = '50%';
                rightPanel.style.width = '50%';
            }
        });
    }

    // 初始化主题
    function initializeTheme() {
        const savedTheme = localStorage.getItem('textCompareTheme');
        if (savedTheme === 'dark') {
            toggleTheme();
        }
    }

    // 文本对比工具初始化
    function initTextCompareTool() {
        // 检查是否在文本对比工具页面
        const textCompareTool = document.getElementById('text-compare-tool');
        if (!textCompareTool) return;

        initializeTextCompare();
        initializeTheme();

        // 初始状态保存
        saveState();
        updateUndoRedoButtons();

        console.log('文本对比工具已初始化');
    }

    // 监听侧边栏点击，当切换到文本对比工具时初始化
    const textCompareLink = document.querySelector('[data-tool="text-compare-tool"]');
    if (textCompareLink) {
        textCompareLink.addEventListener('click', () => {
            setTimeout(initTextCompareTool, 100); // 延迟初始化确保DOM已更新
        });
    }

    // --- 初始化 ---
    const activeLink = document.querySelector('.sidebar-link.active');
    if (activeLink) {
        activeLink.click();
    }

    // 如果当前就在文本对比工具页面，立即初始化
    if (document.querySelector('.sidebar-link[data-tool="text-compare-tool"].active')) {
        initTextCompareTool();
    }

    // --- 文本转换工具逻辑 ---
    function initTextConvertTool() {
        // 选项卡切换功能
        const tabButtons = document.querySelectorAll('.text-convert-tab-btn');
        const tabContents = document.querySelectorAll('.text-convert-tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');

                // 更新按钮状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                // 更新内容显示
                tabContents.forEach(content => {
                    if (content.id === targetTab) {
                        content.classList.remove('tab-hidden');
                    } else {
                        content.classList.add('tab-hidden');
                    }
                });
            });
        });

        // 初始化各个功能模块
        initEncodingDetection();
        initHtmlEntityConverter();
        initUrlEncoder();
        initUnicodeConverter();
    }

    // 字符编码检测功能
    function initEncodingDetection() {
        const encodingInput = document.getElementById('encoding-input');
        const detectBtn = document.getElementById('detect-encoding-btn');
        const clearBtn = document.getElementById('clear-encoding-btn');
        const encodingResult = document.getElementById('encoding-result');
        const convertBtn = document.getElementById('convert-encoding-btn');
        const sourceEncoding = document.getElementById('source-encoding');
        const targetEncoding = document.getElementById('target-encoding');
        const convertOutput = document.getElementById('encoding-convert-output');

        if (detectBtn) {
            detectBtn.addEventListener('click', async () => {
                const text = encodingInput.value.trim();
                if (!text) {
                    showTextConvertMessage('请输入要检测的文本', 'error');
                    return;
                }

                try {
                    detectBtn.disabled = true;
                    detectBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>检测中...';

                    // 调用后端API进行编码检测
                    const response = await fetch('/api/v1/text-convert/detect-encoding', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        displayEncodingResults(result.results);
                        showTextConvertMessage('编码检测完成', 'success');
                    } else {
                        throw new Error(result.error || '检测失败');
                    }

                } catch (error) {
                    showTextConvertMessage('编码检测失败: ' + error.message, 'error');
                } finally {
                    detectBtn.disabled = false;
                    detectBtn.innerHTML = '<i class="fas fa-search mr-2"></i>检测编码';
                }
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                encodingInput.value = '';
                encodingResult.innerHTML = `
                    <div class="text-center text-gray-400 py-8">
                        <i class="fas fa-code text-3xl mb-2"></i>
                        <p>请输入文本进行编码检测</p>
                    </div>`;
                convertOutput.value = '';
            });
        }

        if (convertBtn) {
            convertBtn.addEventListener('click', async () => {
                const text = encodingInput.value.trim();
                const source = sourceEncoding.value;
                const target = targetEncoding.value;

                if (!text) {
                    showTextConvertMessage('请输入要转换的文本', 'error');
                    return;
                }

                try {
                    // 调用后端API进行编码转换
                    const response = await fetch('/api/v1/text-convert/convert-encoding', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            source_encoding: source,
                            target_encoding: target
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        convertOutput.value = result.result;
                        showTextConvertMessage('编码转换完成', 'success');
                    } else {
                        throw new Error(result.error || '转换失败');
                    }
                } catch (error) {
                    showTextConvertMessage('编码转换失败: ' + error.message, 'error');
                }
            });
        }
    }



    // 显示编码检测结果
    function displayEncodingResults(results) {
        const encodingResult = document.getElementById('encoding-result');
        let html = '';

        results.forEach(result => {
            const confidenceClass = `confidence-${result.confidence}`;
            const score = result.confidence_score || 0;
            html += `
                <div class="encoding-info">
                    <div class="flex items-center justify-between mb-2">
                        <span class="font-semibold text-blue-800">${result.encoding}</span>
                        <span class="encoding-confidence ${confidenceClass}">${result.confidence} (${score}%)</span>
                    </div>
                    <p class="text-sm text-blue-600">${result.description}</p>
                </div>
            `;
        });

        encodingResult.innerHTML = html;
    }

    // 显示Unicode字符信息（使用后端返回的数据）
    function displayUnicodeCharInfo(charInfoList) {
        const unicodeCharDetails = document.getElementById('unicode-char-details');
        if (!unicodeCharDetails || !charInfoList || charInfoList.length === 0) {
            if (unicodeCharDetails) {
                unicodeCharDetails.innerHTML = `
                    <div class="text-center text-gray-400 py-4">
                        输入文本后显示字符详细信息
                    </div>`;
            }
            return;
        }

        let html = '';
        charInfoList.forEach(charInfo => {
            html += `
                <div class="unicode-char-item">
                    <div class="flex items-center justify-between">
                        <span class="char">${charInfo.char}</span>
                        <div class="text-right">
                            <div class="code">${charInfo.unicode_code}</div>
                            <div class="code">\\u${charInfo.hex_code.padStart(4, '0')}</div>
                            <div class="code">&#${charInfo.decimal_code};</div>
                        </div>
                    </div>
                    <div class="name mt-1">${charInfo.name}</div>
                </div>
            `;
        });

        unicodeCharDetails.innerHTML = html;
    }

    // HTML实体编码/解码功能
    function initHtmlEntityConverter() {
        const htmlInput = document.getElementById('html-entity-input');
        const htmlOutput = document.getElementById('html-entity-output');
        const encodeBtn = document.getElementById('html-encode-btn');
        const decodeBtn = document.getElementById('html-decode-btn');
        const clearBtn = document.getElementById('clear-html-entity-btn');
        const copyBtn = document.getElementById('copy-html-entity-btn');
        const exampleBtn = document.getElementById('load-html-example-btn');

        if (encodeBtn) {
            encodeBtn.addEventListener('click', async () => {
                const text = htmlInput.value;
                if (!text.trim()) {
                    showTextConvertMessage('请输入要编码的文本', 'error');
                    return;
                }

                try {
                    // 调用后端API进行HTML实体编码
                    const response = await fetch('/api/v1/text-convert/html-entity', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'encode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        htmlOutput.value = result.result;
                        showTextConvertMessage('HTML实体编码完成', 'success');
                    } else {
                        throw new Error(result.error || '编码失败');
                    }
                } catch (error) {
                    showTextConvertMessage('编码失败: ' + error.message, 'error');
                }
            });
        }

        if (decodeBtn) {
            decodeBtn.addEventListener('click', async () => {
                const text = htmlInput.value;
                if (!text.trim()) {
                    showTextConvertMessage('请输入要解码的HTML实体', 'error');
                    return;
                }

                try {
                    // 调用后端API进行HTML实体解码
                    const response = await fetch('/api/v1/text-convert/html-entity', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'decode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        htmlOutput.value = result.result;
                        showTextConvertMessage('HTML实体解码完成', 'success');
                    } else {
                        throw new Error(result.error || '解码失败');
                    }
                } catch (error) {
                    showTextConvertMessage('解码失败: ' + error.message, 'error');
                }
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                htmlInput.value = '';
                htmlOutput.value = '';
            });
        }

        if (copyBtn) {
            copyBtn.addEventListener('click', () => {
                if (htmlOutput.value) {
                    navigator.clipboard.writeText(htmlOutput.value)
                        .then(() => window.showCopyModal())
                        .catch(err => console.error('复制失败:', err));
                }
            });
        }

        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => {
                htmlInput.value = MOCK_HTML_ENTITY_TEXT;
                showTextConvertMessage('已载入示例数据', 'success');
            });
        }
    }



    // URL编码/解码功能
    function initUrlEncoder() {
        const urlInput = document.getElementById('url-encode-input');
        const urlOutput = document.getElementById('url-encode-output');
        const encodeBtn = document.getElementById('url-encode-btn');
        const decodeBtn = document.getElementById('url-decode-btn');
        const clearBtn = document.getElementById('clear-url-encode-btn');
        const copyBtn = document.getElementById('copy-url-encode-btn');
        const exampleBtn = document.getElementById('load-url-example-btn');

        if (encodeBtn) {
            encodeBtn.addEventListener('click', async () => {
                const text = urlInput.value;
                if (!text.trim()) {
                    showTextConvertMessage('请输入要编码的文本', 'error');
                    return;
                }

                try {
                    // 调用后端API进行URL编码
                    const response = await fetch('/api/v1/text-convert/url-encode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'encode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        urlOutput.value = result.result;
                        showTextConvertMessage('URL编码完成', 'success');
                    } else {
                        throw new Error(result.error || '编码失败');
                    }
                } catch (error) {
                    showTextConvertMessage('编码失败: ' + error.message, 'error');
                }
            });
        }

        if (decodeBtn) {
            decodeBtn.addEventListener('click', async () => {
                const text = urlInput.value;
                if (!text.trim()) {
                    showTextConvertMessage('请输入要解码的URL编码', 'error');
                    return;
                }

                try {
                    // 调用后端API进行URL解码
                    const response = await fetch('/api/v1/text-convert/url-encode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'decode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        urlOutput.value = result.result;
                        showTextConvertMessage('URL解码完成', 'success');
                    } else {
                        throw new Error(result.error || '解码失败');
                    }
                } catch (error) {
                    showTextConvertMessage('解码失败: ' + error.message, 'error');
                }
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                urlInput.value = '';
                urlOutput.value = '';
            });
        }

        if (copyBtn) {
            copyBtn.addEventListener('click', () => {
                if (urlOutput.value) {
                    navigator.clipboard.writeText(urlOutput.value)
                        .then(() => window.showCopyModal())
                        .catch(err => console.error('复制失败:', err));
                }
            });
        }

        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => {
                urlInput.value = MOCK_URL_ENCODE_TEXT;
                showTextConvertMessage('已载入示例数据', 'success');
            });
        }
    }

    // Unicode编码/解码功能
    function initUnicodeConverter() {
        const unicodeInput = document.getElementById('unicode-input');
        const unicodeOutput = document.getElementById('unicode-output');
        const encodeBtn = document.getElementById('unicode-encode-btn');
        const decodeBtn = document.getElementById('unicode-decode-btn');
        const clearBtn = document.getElementById('clear-unicode-btn');
        const copyBtn = document.getElementById('copy-unicode-btn');
        const exampleBtn = document.getElementById('load-unicode-example-btn');
        const unicodeCharDetails = document.getElementById('unicode-char-details');

        if (encodeBtn) {
            encodeBtn.addEventListener('click', async () => {
                const text = unicodeInput.value;
                if (!text.trim()) {
                    showTextConvertMessage('请输入要编码的文本', 'error');
                    return;
                }

                try {
                    const format = document.querySelector('input[name="unicode-format"]:checked').value;

                    // 调用后端API进行Unicode编码
                    const response = await fetch('/api/v1/text-convert/unicode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            operation: 'encode',
                            format: format
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        unicodeOutput.value = result.result;
                        displayUnicodeCharInfo(result.char_info);
                        showTextConvertMessage('Unicode编码完成', 'success');
                    } else {
                        throw new Error(result.error || '编码失败');
                    }
                } catch (error) {
                    showTextConvertMessage('编码失败: ' + error.message, 'error');
                }
            });
        }

        if (decodeBtn) {
            decodeBtn.addEventListener('click', async () => {
                const text = unicodeInput.value;
                if (!text.trim()) {
                    showTextConvertMessage('请输入要解码的Unicode编码', 'error');
                    return;
                }

                try {
                    // 调用后端API进行Unicode解码
                    const response = await fetch('/api/v1/text-convert/unicode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            operation: 'decode',
                            format: '\\u'  // 解码时格式不重要
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        unicodeOutput.value = result.result;
                        displayUnicodeCharInfo(result.char_info);
                        showTextConvertMessage('Unicode解码完成', 'success');
                    } else {
                        throw new Error(result.error || '解码失败');
                    }
                } catch (error) {
                    showTextConvertMessage('解码失败: ' + error.message, 'error');
                }
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                unicodeInput.value = '';
                unicodeOutput.value = '';
                unicodeCharDetails.innerHTML = `
                    <div class="text-center text-gray-400 py-4">
                        输入文本后显示字符详细信息
                    </div>`;
            });
        }

        if (copyBtn) {
            copyBtn.addEventListener('click', () => {
                if (unicodeOutput.value) {
                    navigator.clipboard.writeText(unicodeOutput.value)
                        .then(() => window.showCopyModal())
                        .catch(err => console.error('复制失败:', err));
                }
            });
        }

        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => {
                unicodeInput.value = MOCK_UNICODE_TEXT;
                showTextConvertMessage('已载入示例数据', 'success');
            });
        }

        // 实时更新字符信息
        if (unicodeInput) {
            unicodeInput.addEventListener('input', async () => {
                const text = unicodeInput.value;
                if (text && text.length <= 50) { // 限制长度避免频繁请求
                    try {
                        const response = await fetch('/api/v1/text-convert/unicode', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                text: text,
                                operation: 'encode',
                                format: '\\u'
                            })
                        });

                        if (response.ok) {
                            const result = await response.json();
                            if (result.success) {
                                displayUnicodeCharInfo(result.char_info);
                            }
                        }
                    } catch (error) {
                        console.log('实时更新字符信息失败:', error);
                    }
                }
            });
        }
    }



    // 显示文本转换消息
    function showTextConvertMessage(message, type = 'info') {
        const messageArea = document.getElementById('text-convert-message-area');
        if (!messageArea) return;

        messageArea.textContent = message;
        messageArea.className = `text-center text-sm h-5 mb-2 flex-shrink-0 ${
            type === 'error' ? 'text-red-600' :
            type === 'success' ? 'text-green-600' :
            'text-blue-600'
        }`;

        // 3秒后清除消息
        setTimeout(() => {
            messageArea.textContent = '';
            messageArea.className = 'text-center text-sm text-red-600 h-5 mb-2 flex-shrink-0';
        }, 3000);
    }

    // --- 父子级导航功能 ---
    function initParentChildNavigation() {
        const parentHeaders = document.querySelectorAll('.nav-parent-header');

        parentHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const parentId = header.getAttribute('data-parent');
                const children = document.querySelector(`[data-parent-children="${parentId}"]`);
                const arrow = header.querySelector('.nav-parent-arrow');

                // 移除其他父级的active状态
                document.querySelectorAll('.nav-parent-header').forEach(h => h.classList.remove('active'));
                document.querySelectorAll('.sidebar-link').forEach(link => link.classList.remove('active'));

                // 添加当前父级的active状态
                header.classList.add('active');

                if (children) {
                    const isCollapsed = children.classList.contains('collapsed');

                    if (isCollapsed) {
                        // 展开
                        children.classList.remove('collapsed');
                        arrow.classList.remove('rotated');
                        localStorage.setItem(`nav-${parentId}-expanded`, 'true');
                    } else {
                        // 折叠
                        children.classList.add('collapsed');
                        arrow.classList.add('rotated');
                        localStorage.setItem(`nav-${parentId}-expanded`, 'false');
                    }
                }
            });
        });

        // 恢复保存的展开/折叠状态
        parentHeaders.forEach(header => {
            const parentId = header.getAttribute('data-parent');
            const children = document.querySelector(`[data-parent-children="${parentId}"]`);
            const arrow = header.querySelector('.nav-parent-arrow');
            const savedState = localStorage.getItem(`nav-${parentId}-expanded`);

            // 默认展开，除非明确保存为折叠状态
            if (savedState === 'false') {
                children?.classList.add('collapsed');
                arrow?.classList.add('rotated');
            }
        });
    }

    // --- 编码解码工具初始化 ---
    function initEncodingTools() {
        initEncodingDetectionTool();
        initHtmlEntityTool();
        initUrlEncodeTool();
        initUnicodeTool();
    }

    // 字符编码检测工具
    function initEncodingDetectionTool() {
        const detectBtn = document.getElementById('detect-encoding-btn');
        const clearBtn = document.getElementById('clear-encoding-input-btn');
        const exampleBtn = document.getElementById('load-encoding-example-btn');
        const input = document.getElementById('encoding-detection-input');
        const result = document.getElementById('encoding-detection-result');
        const convertBtn = document.getElementById('convert-encoding-btn');
        const sourceSelect = document.getElementById('source-encoding-select');
        const targetSelect = document.getElementById('target-encoding-select');
        const convertResult = document.getElementById('encoding-convert-result');

        if (detectBtn) {
            detectBtn.addEventListener('click', async () => {
                const text = input.value.trim();
                if (!text) {
                    showMessage('请输入要检测的文本', 'error');
                    return;
                }

                try {
                    detectBtn.disabled = true;
                    detectBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>检测中...';

                    const response = await fetch('/api/v1/text-convert/detect-encoding', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        displayEncodingResults(data.results);
                        showMessage('编码检测完成', 'success');
                    } else {
                        throw new Error(data.error || '检测失败');
                    }
                } catch (error) {
                    showMessage('编码检测失败: ' + error.message, 'error');
                } finally {
                    detectBtn.disabled = false;
                    detectBtn.innerHTML = '<i class="fas fa-search mr-2"></i>检测编码';
                }
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                input.value = '';
                result.innerHTML = `
                    <div class="text-center text-gray-400 py-8">
                        <i class="fas fa-code text-3xl mb-2"></i>
                        <p>请输入文本进行编码检测</p>
                    </div>`;
                convertResult.value = '';
            });
        }

        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => {
                input.value = ENCODING_EXAMPLES.encoding;
                showMessage('已载入示例数据', 'success');
            });
        }

        if (convertBtn) {
            convertBtn.addEventListener('click', async () => {
                const text = input.value.trim();
                const source = sourceSelect.value;
                const target = targetSelect.value;

                if (!text) {
                    showMessage('请输入要转换的文本', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/text-convert/convert-encoding', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            source_encoding: source,
                            target_encoding: target
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        convertResult.value = data.result;
                        showMessage('编码转换完成', 'success');
                    } else {
                        throw new Error(data.error || '转换失败');
                    }
                } catch (error) {
                    showMessage('编码转换失败: ' + error.message, 'error');
                }
            });
        }
    }

    // HTML实体编码工具
    function initHtmlEntityTool() {
        const encodeBtn = document.getElementById('html-entity-encode-btn');
        const decodeBtn = document.getElementById('html-entity-decode-btn');
        const input = document.getElementById('html-entity-input');
        const output = document.getElementById('html-entity-output');
        const clearInputBtn = document.getElementById('clear-html-entity-input-btn');
        const clearOutputBtn = document.getElementById('clear-html-entity-output-btn');
        const copyInputBtn = document.getElementById('copy-html-entity-input-btn');
        const copyOutputBtn = document.getElementById('copy-html-entity-output-btn');
        const exampleBtn = document.getElementById('load-html-entity-example-btn');
        const encodedExampleBtn = document.getElementById('load-html-entity-encoded-example-btn');

        if (encodeBtn) {
            encodeBtn.addEventListener('click', async () => {
                const text = input.value;
                if (!text.trim()) {
                    showMessage('请输入要编码的文本', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/text-convert/html-entity', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'encode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        output.value = data.result;
                        showMessage('HTML实体编码完成', 'success');
                    } else {
                        throw new Error(data.error || '编码失败');
                    }
                } catch (error) {
                    showMessage('编码失败: ' + error.message, 'error');
                }
            });
        }

        if (decodeBtn) {
            decodeBtn.addEventListener('click', async () => {
                const text = output.value;
                if (!text.trim()) {
                    showMessage('请输入要解码的HTML实体', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/text-convert/html-entity', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'decode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        input.value = data.result;
                        showMessage('HTML实体解码完成', 'success');
                    } else {
                        throw new Error(data.error || '解码失败');
                    }
                } catch (error) {
                    showMessage('解码失败: ' + error.message, 'error');
                }
            });
        }

        // 清空和复制按钮
        if (clearInputBtn) clearInputBtn.addEventListener('click', () => input.value = '');
        if (clearOutputBtn) clearOutputBtn.addEventListener('click', () => output.value = '');
        if (copyInputBtn) copyInputBtn.addEventListener('click', () => copyToClipboard(input.value));
        if (copyOutputBtn) copyOutputBtn.addEventListener('click', () => copyToClipboard(output.value));

        // 示例按钮
        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => {
                input.value = ENCODING_EXAMPLES.htmlEntity.original;
                showMessage('已载入原始示例', 'success');
            });
        }

        if (encodedExampleBtn) {
            encodedExampleBtn.addEventListener('click', () => {
                output.value = ENCODING_EXAMPLES.htmlEntity.encoded;
                showMessage('已载入编码示例', 'success');
            });
        }
    }

    // URL编码工具
    function initUrlEncodeTool() {
        const encodeBtn = document.getElementById('url-encode-btn');
        const decodeBtn = document.getElementById('url-decode-btn');
        const input = document.getElementById('url-input');
        const output = document.getElementById('url-output');
        const clearInputBtn = document.getElementById('clear-url-input-btn');
        const clearOutputBtn = document.getElementById('clear-url-output-btn');
        const copyInputBtn = document.getElementById('copy-url-input-btn');
        const copyOutputBtn = document.getElementById('copy-url-output-btn');
        const exampleBtn = document.getElementById('load-url-example-btn');
        const encodedExampleBtn = document.getElementById('load-url-encoded-example-btn');

        if (encodeBtn) {
            encodeBtn.addEventListener('click', async () => {
                const text = input.value;
                if (!text.trim()) {
                    showMessage('请输入要编码的文本', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/text-convert/url-encode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'encode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        output.value = data.result;
                        showMessage('URL编码完成', 'success');
                    } else {
                        throw new Error(data.error || '编码失败');
                    }
                } catch (error) {
                    showMessage('编码失败: ' + error.message, 'error');
                }
            });
        }

        if (decodeBtn) {
            decodeBtn.addEventListener('click', async () => {
                const text = output.value;
                if (!text.trim()) {
                    showMessage('请输入要解码的URL编码', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/text-convert/url-encode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'decode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        input.value = data.result;
                        showMessage('URL解码完成', 'success');
                    } else {
                        throw new Error(data.error || '解码失败');
                    }
                } catch (error) {
                    showMessage('解码失败: ' + error.message, 'error');
                }
            });
        }

        // 清空和复制按钮
        if (clearInputBtn) clearInputBtn.addEventListener('click', () => input.value = '');
        if (clearOutputBtn) clearOutputBtn.addEventListener('click', () => output.value = '');
        if (copyInputBtn) copyInputBtn.addEventListener('click', () => copyToClipboard(input.value));
        if (copyOutputBtn) copyOutputBtn.addEventListener('click', () => copyToClipboard(output.value));

        // 示例按钮
        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => {
                input.value = ENCODING_EXAMPLES.urlEncode.original;
                showMessage('已载入原始示例', 'success');
            });
        }

        if (encodedExampleBtn) {
            encodedExampleBtn.addEventListener('click', () => {
                output.value = ENCODING_EXAMPLES.urlEncode.encoded;
                showMessage('已载入编码示例', 'success');
            });
        }
    }

    // Unicode编码工具
    function initUnicodeTool() {
        const encodeBtn = document.getElementById('unicode-encode-btn');
        const decodeBtn = document.getElementById('unicode-decode-btn');
        const input = document.getElementById('unicode-input');
        const output = document.getElementById('unicode-output');
        const clearInputBtn = document.getElementById('clear-unicode-input-btn');
        const clearOutputBtn = document.getElementById('clear-unicode-output-btn');
        const copyInputBtn = document.getElementById('copy-unicode-input-btn');
        const copyOutputBtn = document.getElementById('copy-unicode-output-btn');
        const exampleBtn = document.getElementById('load-unicode-example-btn');
        const encodedExampleBtn = document.getElementById('load-unicode-encoded-example-btn');
        const charDetails = document.getElementById('unicode-char-details');

        if (encodeBtn) {
            encodeBtn.addEventListener('click', async () => {
                const text = input.value;
                if (!text.trim()) {
                    showMessage('请输入要编码的文本', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/text-convert/unicode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            operation: 'encode',
                            format: '\\u'  // 固定使用\u格式
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        output.value = data.result;
                        showMessage('Unicode编码完成', 'success');
                    } else {
                        throw new Error(data.error || '编码失败');
                    }
                } catch (error) {
                    showMessage('编码失败: ' + error.message, 'error');
                }
            });
        }

        if (decodeBtn) {
            decodeBtn.addEventListener('click', async () => {
                // 优先从右侧读取，如果右侧为空则从左侧读取
                let text = output.value.trim();
                if (!text) {
                    text = input.value.trim();
                }

                if (!text) {
                    showMessage('请输入要解码的Unicode编码', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/text-convert/unicode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            operation: 'decode',
                            format: '\\u'
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        // 如果是从右侧解码，结果放到左侧；如果是从左侧解码，结果放到右侧
                        if (output.value.trim()) {
                            input.value = data.result;
                        } else {
                            output.value = data.result;
                        }
                        showMessage('Unicode解码完成', 'success');
                    } else {
                        throw new Error(data.error || '解码失败');
                    }
                } catch (error) {
                    showMessage('解码失败: ' + error.message, 'error');
                }
            });
        }

        // 清空和复制按钮
        if (clearInputBtn) clearInputBtn.addEventListener('click', () => input.value = '');
        if (clearOutputBtn) clearOutputBtn.addEventListener('click', () => output.value = '');
        if (copyInputBtn) copyInputBtn.addEventListener('click', () => copyToClipboard(input.value));
        if (copyOutputBtn) copyOutputBtn.addEventListener('click', () => copyToClipboard(output.value));

        // 示例按钮
        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => {
                input.value = ENCODING_EXAMPLES.unicode.original;
                showMessage('已载入原始示例', 'success');
            });
        }

        if (encodedExampleBtn) {
            encodedExampleBtn.addEventListener('click', () => {
                output.value = ENCODING_EXAMPLES.unicode.encoded;
                showMessage('已载入编码示例', 'success');
            });
        }
    }

    // 辅助函数
    function copyToClipboard(text) {
        if (text) {
            navigator.clipboard.writeText(text)
                .then(() => window.showCopyModal())
                .catch(err => console.error('复制失败:', err));
        }
    }

    function showMessage(message, type = 'info') {
        // 尝试多个可能的消息区域
        const messageAreas = [
            document.getElementById('encoding-detection-message'),
            document.getElementById('html-entity-input-message'),
            document.getElementById('url-input-message'),
            document.getElementById('unicode-input-message'),
            document.getElementById('timestamp-input-message'),
            document.getElementById('date-input-message'),
            document.getElementById('date-diff-message'),
            document.getElementById('date-offset-message')
        ];

        messageAreas.forEach(area => {
            if (area) {
                area.textContent = message;
                area.className = `mt-2 text-sm h-5 flex-shrink-0 ${
                    type === 'error' ? 'text-red-600' :
                    type === 'success' ? 'text-green-600' :
                    'text-blue-600'
                }`;

                setTimeout(() => {
                    area.textContent = '';
                    area.className = 'mt-2 text-sm text-gray-500 h-5 flex-shrink-0';
                }, 3000);
            }
        });
    }

    // --- 时间转换工具初始化 ---
    function initTimestampTool() {
        const tsToDateBtn = document.getElementById('ts-to-date-btn');
        const dateToTsBtn = document.getElementById('date-to-ts-btn');
        const clearTimestampBtn = document.getElementById('clear-timestamp-btn');
        const clearDateBtn = document.getElementById('clear-date-btn');
        const loadTimestampExampleBtn = document.getElementById('load-timestamp-example-btn');
        const loadDateExampleBtn = document.getElementById('load-date-example-btn');

        // 实时显示当前时间
        function updateCurrentTime() {
            const now = new Date();
            const timestamp = Math.floor(now.getTime() / 1000);
            const dateStr = now.getFullYear() + '-' +
                          String(now.getMonth() + 1).padStart(2, '0') + '-' +
                          String(now.getDate()).padStart(2, '0') + ' ' +
                          String(now.getHours()).padStart(2, '0') + ':' +
                          String(now.getMinutes()).padStart(2, '0') + ':' +
                          String(now.getSeconds()).padStart(2, '0');

            // 更新显示区域
            const tsOutput = document.getElementById('ts-to-date-output');
            const dateOutput = document.getElementById('date-to-ts-output');

            if (tsOutput && !tsOutput.textContent.trim()) {
                tsOutput.innerHTML = `<div class="text-gray-500 text-xs">当前时间戳: ${timestamp}</div>`;
            }
            if (dateOutput && !dateOutput.textContent.trim()) {
                dateOutput.innerHTML = `<div class="text-gray-500 text-xs">当前时间: ${dateStr}</div>`;
            }
        }

        // 每秒更新时间
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();

        // 时间戳转日期
        if (tsToDateBtn) {
            tsToDateBtn.addEventListener('click', async () => {
                const input = document.getElementById('ts-to-date-input');
                const output = document.getElementById('ts-to-date-output');

                if (!input.value.trim()) {
                    showMessage('请输入时间戳', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/datetime/ts-to-date', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ timestamp_str: input.value })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.error) {
                        showMessage('转换失败: ' + data.error, 'error');
                    } else {
                        output.innerHTML = `
                            <div class="space-y-2">
                                <div><strong>时间戳(秒):</strong> ${data.result.timestamp_in_seconds}</div>
                                <div><strong>UTC时间:</strong> ${data.result.utc_datetime}</div>
                                <div><strong>本地时间:</strong> ${data.result.local_datetime}</div>
                            </div>`;
                        showMessage('时间戳转换完成', 'success');
                    }
                } catch (error) {
                    showMessage('转换失败: ' + error.message, 'error');
                }
            });
        }

        // 日期转时间戳
        if (dateToTsBtn) {
            dateToTsBtn.addEventListener('click', async () => {
                const input = document.getElementById('date-to-ts-input');
                const output = document.getElementById('date-to-ts-output');

                if (!input.value.trim()) {
                    showMessage('请输入日期时间', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/datetime/date-to-ts', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ datetime_str: input.value })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.error) {
                        showMessage('转换失败: ' + data.error, 'error');
                    } else {
                        output.innerHTML = `
                            <div class="space-y-2">
                                <div><strong>时间戳(秒):</strong> ${data.result.timestamp_in_seconds}</div>
                                <div><strong>时间戳(毫秒):</strong> ${data.result.timestamp_in_milliseconds}</div>
                            </div>`;
                        showMessage('日期转换完成', 'success');
                    }
                } catch (error) {
                    showMessage('转换失败: ' + error.message, 'error');
                }
            });
        }

        // 清空按钮
        if (clearTimestampBtn) {
            clearTimestampBtn.addEventListener('click', () => {
                document.getElementById('ts-to-date-input').value = '';
                document.getElementById('ts-to-date-output').innerHTML = '';
                updateCurrentTime();
            });
        }

        if (clearDateBtn) {
            clearDateBtn.addEventListener('click', () => {
                document.getElementById('date-to-ts-input').value = '';
                document.getElementById('date-to-ts-output').innerHTML = '';
                updateCurrentTime();
            });
        }

        // 载入示例按钮
        if (loadTimestampExampleBtn) {
            loadTimestampExampleBtn.addEventListener('click', () => {
                document.getElementById('ts-to-date-input').value = '1640995200';
                showMessage('已载入时间戳示例', 'success');
            });
        }

        if (loadDateExampleBtn) {
            loadDateExampleBtn.addEventListener('click', () => {
                document.getElementById('date-to-ts-input').value = '2022-01-01 00:00:00';
                showMessage('已载入日期示例', 'success');
            });
        }
    }

    // --- 日期计算器工具初始化 ---
    function initDateCalculatorTool() {
        const dateDiffBtn = document.getElementById('date-diff-btn');
        const dateOffsetBtn = document.getElementById('date-offset-btn');
        const clearDateDiffBtn = document.getElementById('clear-date-diff-btn');
        const clearDateOffsetBtn = document.getElementById('clear-date-offset-btn');
        const loadDateDiffExampleBtn = document.getElementById('load-date-diff-example-btn');
        const loadDateOffsetExampleBtn = document.getElementById('load-date-offset-example-btn');

        // 实时显示当前时间
        function updateCurrentTime() {
            const now = new Date();
            const dateStr = now.getFullYear() + '-' +
                          String(now.getMonth() + 1).padStart(2, '0') + '-' +
                          String(now.getDate()).padStart(2, '0') + ' ' +
                          String(now.getHours()).padStart(2, '0') + ':' +
                          String(now.getMinutes()).padStart(2, '0') + ':' +
                          String(now.getSeconds()).padStart(2, '0');

            // 更新显示区域
            const diffOutput = document.getElementById('date-diff-output');
            const offsetOutput = document.getElementById('date-offset-output');

            if (diffOutput && !diffOutput.textContent.trim()) {
                diffOutput.innerHTML = `<div class="text-gray-500 text-xs">当前时间: ${dateStr}</div>`;
            }
            if (offsetOutput && !offsetOutput.textContent.trim()) {
                offsetOutput.innerHTML = `<div class="text-gray-500 text-xs">当前时间: ${dateStr}</div>`;
            }
        }

        // 每秒更新时间
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();

        // 设置默认值
        const now = new Date();
        const currentDateStr = now.getFullYear() + '-' +
                              String(now.getMonth() + 1).padStart(2, '0') + '-' +
                              String(now.getDate()).padStart(2, '0') + ' ' +
                              String(now.getHours()).padStart(2, '0') + ':' +
                              String(now.getMinutes()).padStart(2, '0') + ':' +
                              String(now.getSeconds()).padStart(2, '0');

        // 设置默认值
        const diffStartDate = document.getElementById('diff-start-date');
        const offsetStartDate = document.getElementById('offset-start-date');

        if (diffStartDate && !diffStartDate.value) {
            diffStartDate.value = '2020-01-01 12:00:00';
        }
        if (offsetStartDate && !offsetStartDate.value) {
            offsetStartDate.value = currentDateStr;
        }

        // 日期差计算
        if (dateDiffBtn) {
            dateDiffBtn.addEventListener('click', async () => {
                const startInput = document.getElementById('diff-start-date');
                const endInput = document.getElementById('diff-end-date');
                const output = document.getElementById('date-diff-output');

                if (!startInput.value.trim()) {
                    showMessage('请输入起始日期', 'error');
                    return;
                }

                let endDate = endInput.value.trim();
                if (!endDate) {
                    endDate = currentDateStr;
                }

                try {
                    const response = await fetch('/api/v1/datetime/diff', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            start_date: startInput.value,
                            end_date: endDate
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.error) {
                        showMessage('计算失败: ' + data.error, 'error');
                    } else {
                        output.textContent = data.result;
                        showMessage('日期差计算完成', 'success');
                    }
                } catch (error) {
                    showMessage('计算失败: ' + error.message, 'error');
                }
            });
        }

        // 日期偏移计算
        if (dateOffsetBtn) {
            dateOffsetBtn.addEventListener('click', async () => {
                const startInput = document.getElementById('offset-start-date');
                const daysInput = document.getElementById('offset-days');
                const output = document.getElementById('date-offset-output');

                if (!startInput.value.trim()) {
                    showMessage('请输入起始日期', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/datetime/offset', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            start_date: startInput.value,
                            days: parseInt(daysInput.value) || 0
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.error) {
                        showMessage('计算失败: ' + data.error, 'error');
                    } else {
                        output.textContent = data.result;
                        showMessage('日期计算完成', 'success');
                    }
                } catch (error) {
                    showMessage('计算失败: ' + error.message, 'error');
                }
            });
        }

        // 清空按钮
        if (clearDateDiffBtn) {
            clearDateDiffBtn.addEventListener('click', () => {
                document.getElementById('diff-start-date').value = '2020-01-01 12:00:00';
                document.getElementById('diff-end-date').value = '';
                document.getElementById('date-diff-output').innerHTML = '';
                updateCurrentTime();
            });
        }

        if (clearDateOffsetBtn) {
            clearDateOffsetBtn.addEventListener('click', () => {
                document.getElementById('offset-start-date').value = currentDateStr;
                document.getElementById('offset-days').value = '195';
                document.getElementById('date-offset-output').innerHTML = '';
                updateCurrentTime();
            });
        }

        // 载入示例按钮
        if (loadDateDiffExampleBtn) {
            loadDateDiffExampleBtn.addEventListener('click', () => {
                document.getElementById('diff-start-date').value = '2020-01-01 12:00:00';
                document.getElementById('diff-end-date').value = '2025-06-26 19:18:35';
                showMessage('已载入日期差计算示例', 'success');
            });
        }

        if (loadDateOffsetExampleBtn) {
            loadDateOffsetExampleBtn.addEventListener('click', () => {
                document.getElementById('offset-start-date').value = '2025-06-26 19:18:34';
                document.getElementById('offset-days').value = '195';
                showMessage('已载入日期偏移示例', 'success');
            });
        }
    }

    // 初始化所有工具
    initParentChildNavigation();
    initEncodingTools();
    initTimestampTool();
    initDateCalculatorTool();

    // 保持原有的文本转换工具初始化（向后兼容）
    const textConvertTool = document.getElementById('text-convert-tool');
    if (textConvertTool) {
        initTextConvertTool();
    }
});