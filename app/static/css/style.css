/* 为活动菜单项添加自定义样式 */
.sidebar-link.active {
    background-color: #3b82f6; /* bg-blue-600 */
    color: white;
}

/* 修复后的悬停逻辑：只对非活动项应用悬停效果 */
.sidebar-link:not(.active):hover {
    background-color: #f3f4f6; /* bg-gray-100 */
}

/* 简单的模态框过渡效果 */
#copy-modal {
    transition: opacity 0.3s ease-in-out;
}

/* 工具栏按钮过渡效果 */
.tool-btn {
    transition: background-color 0.2s, color 0.2s;
}

/* 工具栏按钮激活时的样式 */
.tool-btn.active {
    background-color: #3b82f6; /* bg-blue-600 */
    color: white;
}

/* 防止已激活按钮在鼠标悬停时改变样式 */
.tool-btn.active:hover {
    background-color: #3b82f6; /* 保持激活颜色 */
}

/* JSON折叠内容区域的样式 */
.json-node-content {
    transition: max-height 0.3s ease-in-out;
    overflow: hidden;
}

/* 搜索结果高亮样式 */
.search-highlight {
    background-color: yellow;
    color: black;
    border-radius: 2px;
}

/* --- 日期时间转换工具 Tab 样式 --- */

/* Tab 按钮的通用样式 */
.datetime-tab-btn {
    border: 1px solid #e5e7eb; /* 边框颜色: border-gray-200 */
    border-bottom: none; /* 默认没有下边框，与内容区分开 */
    margin-left: -1px; /* 让相邻边框合并，看起来更整洁 */
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out; /* 平滑过渡效果 */
    outline: none; /* 移除点击时的蓝色轮廓 */
}

.datetime-tab-btn:first-child {
    margin-left: 0; /* 第一个按钮没有左外边距 */
}

/* 非激活状态下的悬停效果 */
.datetime-tab-btn:not(.active):hover {
    background-color: #f9fafb; /* bg-gray-50 */
}

/* 激活状态的样式 */
.datetime-tab-btn.active {
    background-color: #3b82f6; /* 背景色: bg-blue-600 */
    color: white; /* 文字颜色: white */
    border-color: #3b82f6; /* 边框颜色与背景色一致 */
}

/* Tab 内容区的过渡效果 */
.datetime-tab-content {
    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}

/* 用于控制Tab内容淡入淡出的辅助类 */
.tab-hidden {
    opacity: 0;
    visibility: hidden;
    height: 0;
    overflow: hidden;
    padding: 0;
    margin: 0;
    border: none;
}

