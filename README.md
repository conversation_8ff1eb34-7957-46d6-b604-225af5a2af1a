# SpiderTools 项目文档

欢迎来到 SpiderTools！这是一个基于 FastAPI 构建的、专为您定制的Web版爬虫工具箱。

## 1. 项目愿景

旨在提供一个私有化、可扩展的平台，集成各种爬虫开发中常用的小工具，如请求头转换、User-Agent生成、JS加密调用等，以提高开发效率。

## 2. 项目结构详解

我们的项目遵循关注点分离的原则，结构清晰，易于维护。

```
spidertools/
├── app/                  # 【核心】所有应用代码的家
│   ├── api/              # 存放所有API接口（路由）
│   │   └── v1/           # API版本1，未来可以有v2, v3...
│   │       └── endpoints/  # 存放具体功能的接口文件
│   │           └── headers_converter.py  # ▶ headers转换功能的接口逻辑
│   ├── core/             # 存放核心业务逻辑（纯Python函数）
│   │   └── logic/
│   │       └── header_parser.py      # ▶ headers转换的核心处理函数
│   ├── static/           # 存放前端静态资源
│   │   ├── css/          # 存放自定义的CSS文件
│   │   └── js/           # 存放自定义的JavaScript文件
│   └── templates/        # 存放HTML模板文件
│       └── index.html
│
├── main.py               # ▶ FastAPI应用的启动入口
├── requirements.txt      # ▶ 项目的Python依赖包列表
├── scaffold_generator.py # (工具) 用于生成项目结构的脚本
└── PROJECT_README.md     # (文档) 就是您正在阅读的这个文件
```

### 模块说明

- **`main.py`**: 项目的“总司令部”。它负责创建FastAPI应用实例，挂载静态文件目录和模板目录，并包含根路由（比如首页）。
- **`app/templates/`**: 前端页面的家。所有HTML文件都放在这里。我们使用Jinja2模板引擎，可以动态渲染页面。
- **`app/static/`**: 前端“军火库”。CSS样式、JavaScript脚本、图片等不会改变的静态文件都放在这里，并通过`/static`路径对外提供访问。
- **`app/api/`**: 后端“接口服务中心”。所有与前端交互的API都定义在这里。
  - `endpoints/`目录下的每个`.py`文件都代表一个独立的工具或功能模块的API。
- **`app/core/logic/`**: 后端“逻辑处理中心”。这里存放的是纯粹的、不依赖Web框架的Python函数。比如，`header_parser.py`只负责解析字符串，它不知道什么是FastAPI，也不知道什么是HTTP请求。这种设计使得核心逻辑非常容易被测试和复用。

## 3. 如何添加一个新功能？

假设我们要添加一个新工具：“**User-Agent 生成器**”。

1. **第一步：创建核心逻辑**
   - 在 `app/core/logic/` 目录下，新建一个文件 `user_agent_generator.py`。
   - 在这个文件里，编写生成随机User-Agent的Python函数，例如 `def generate_random_ua(): ...`。
2. **第二步：创建API接口**
   - 在 `app/api/v1/endpoints/` 目录下，新建一个文件 `user_agent.py`。
   - 在其中创建一个FastAPI的 `APIRouter`，并定义一个接口，例如 `GET /user-agent/random`。
   - 这个接口会调用 `app.core.logic.user_agent_generator.py` 中的 `generate_random_ua()` 函数，并将结果以JSON格式返回。
3. **第三步：在 `main.py` 中注册新接口**
   - 打开 `main.py` 文件。
   - 导入您刚刚创建的`user_agent`路由，并使用 `app.include_router()` 将它注册到主应用中。
4. **第四步：更新前端页面**
   - 打开 `app/templates/index.html` 文件。
   - 在页面上添加一个新的区域，包括一个“生成UA”的按钮和一个用于显示结果的文本框。
   - 打开 `app/static/js/main.js` 文件。
   - 编写JavaScript代码，监听“生成UA”按钮的点击事件，然后使用 `fetch` 调用我们刚刚创建的 `/api/v1/user-agent/random` 接口，并将返回的UA显示在页面上。

通过以上四步，一个功能完善、前后端分离的新工具就被干净利落地集成到我们的项目中了！



```angular2html

你好，Gemini。

我们需要继续开发“爬虫工具箱 (spidertools)”项目。现在，请你切换到我们约定的【深度调试与稳健开发模式】。

在接下来的所有回答中，请务必遵循以下核心原则：

1.  【全栈思维】在分析任何问题时，必须系统性地考虑前端（HTML/CSS/JS）、后端（Python API/逻辑）和运行环境（缓存/依赖）这三个方面，禁止单点思维。

2.  【日志追踪优先】如果遇到不确定的问题，请不要猜测。你的首选方案应该是建议我在代码的关键路径上（如函数入口、数据转换后、返回前）添加详细的调试打印语句（print() 或 console.log()），让我们通过事实日志来定位问题。

3.  【警惕连锁反应】在进行任何复杂的字符串或正则处理时，必须优先采用我们验证过的、最稳健的策略（如“占位符隔离替换”），以从根源上杜绝“连锁反应”bug。

4.  【完整代码交付】当你提供代码修复方案时，必须提供完整的、可直接替换的文件内容，并清晰标注文件路径。

我们已经完成了HTML格式化工具。现在，我们的新任务是：[在这里详细描述您的下一个开发任务...]

```